#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存优化效果测试脚本
测试新的差异化缓存策略是否有效提升性能
"""

import sys
import os
import time
import logging
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_cache_optimization():
    """测试缓存优化效果"""
    try:
        from app.services.real_scheduling_service import RealSchedulingService
        
        logger.info("Starting cache optimization test...")
        
        # 初始化排产服务
        scheduling_service = RealSchedulingService()
        
        # 测试数据类型列表
        test_data_types = [
            'device_priority',      # 配置数据 - 30分钟TTL
            'equipment_status',     # 业务数据 - 30秒TTL  
            'test_specs',          # 静态数据 - 30分钟TTL
            'uph_data',            # 业务数据 - 5分钟TTL
            'ct_history'           # 静态数据 - 1小时TTL
        ]
        
        # 1. 测试TTL配置是否正确
        logger.info("Test 1: Validating differentiated TTL configuration")
        for data_type in test_data_types:
            ttl = scheduling_service._get_data_ttl(data_type)
            logger.info(f"   {data_type}: {ttl}秒 TTL")
        
        # 2. 测试缓存加载性能
        logger.info("Test 2: Cache loading performance test")
        load_times = {}
        
        for data_type in test_data_types:
            start_time = time.time()
            
            # 第一次加载（缓存未命中）
            data = scheduling_service._get_data_on_demand(data_type)
            first_load_time = time.time() - start_time
            
            start_time = time.time()
            
            # 第二次加载（缓存命中）
            data = scheduling_service._get_data_on_demand(data_type)
            second_load_time = time.time() - start_time
            
            load_times[data_type] = {
                'first_load': first_load_time,
                'second_load': second_load_time,
                'speedup': first_load_time / second_load_time if second_load_time > 0 else float('inf'),
                'data_size': len(data) if isinstance(data, (list, dict)) else 0
            }
            
            logger.info(f"   {data_type}: 首次{first_load_time:.3f}s, 缓存{second_load_time:.3f}s, 加速比{load_times[data_type]['speedup']:.1f}x")
        
        # 3. 测试智能缓存刷新机制
        logger.info("Test 3: Smart cache refresh mechanism")
        for data_type in ['equipment_status', 'device_priority']:
            cache_key = f"{data_type}_0"  # 模拟无上下文的缓存键
            
            # 检查智能刷新逻辑
            needs_refresh = scheduling_service._smart_cache_refresh(data_type, cache_key)
            logger.info(f"   {data_type}: needs refresh = {needs_refresh}")
        
        # 4. 测试访问模式统计
        logger.info("Test 4: Access pattern statistics")
        access_patterns = scheduling_service._data_access_patterns
        for data_type, pattern in access_patterns.items():
            total_access = pattern.get('cache_hits', 0) + pattern.get('cache_misses', 0)
            hit_rate = pattern.get('cache_hits', 0) / total_access if total_access > 0 else 0
            avg_load_time = pattern.get('avg_load_time', 0)
            
            logger.info(f"   {data_type}: hit rate {hit_rate:.1%}, avg load time {avg_load_time:.3f}s, total access {total_access}")
        
        # 5. 性能摘要
        logger.info("Test 5: Performance summary")
        total_speedup = sum(load_times[dt]['speedup'] for dt in load_times if load_times[dt]['speedup'] != float('inf'))
        avg_speedup = total_speedup / len(load_times)
        
        logger.info(f"   Average cache speedup: {avg_speedup:.1f}x")
        logger.info(f"   Test data types: {len(test_data_types)}")
        logger.info(f"   Smart TTL config: enabled")
        logger.info(f"   Smart refresh mechanism: enabled")
        
        # 6. 建议和结论
        logger.info("Optimization recommendations:")
        
        for data_type, metrics in load_times.items():
            if metrics['speedup'] < 2.0:
                logger.warning(f"   - {data_type}: low speedup ({metrics['speedup']:.1f}x), recommend checking cache strategy")
            elif metrics['speedup'] > 10.0:
                logger.info(f"   - {data_type}: excellent cache performance ({metrics['speedup']:.1f}x)")
        
        logger.info("Cache optimization test completed!")
        
        return {
            'ttl_config_ok': True,
            'cache_performance': load_times,
            'avg_speedup': avg_speedup,
            'access_patterns': access_patterns
        }
        
    except Exception as e:
        logger.error(f"Cache optimization test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_cache_ttl_validation():
    """验证TTL配置的正确性"""
    logger.info("Validating TTL configuration correctness...")
    
    try:
        from app.services.real_scheduling_service import RealSchedulingService
        
        service = RealSchedulingService()
        
        # 预期的TTL配置
        expected_ttls = {
            'device_priority': 1800,      # 30分钟
            'lot_priority': 1800,         # 30分钟
            'stage_mapping_config': 3600, # 1小时
            'equipment_status': 30,       # 30秒
            'wait_lots': 60,              # 1分钟
            'uph_data': 300,              # 5分钟
            'test_specs': 1800,           # 30分钟
            'recipe_files': 1800,         # 30分钟
            'ct_history': 3600,           # 1小时
        }
        
        all_correct = True
        
        for data_type, expected_ttl in expected_ttls.items():
            actual_ttl = service._get_data_ttl(data_type)
            
            if actual_ttl == expected_ttl:
                logger.info(f"OK {data_type}: {actual_ttl}s (correct)")
            else:
                logger.error(f"FAIL {data_type}: expected {expected_ttl}s, actual {actual_ttl}s")
                all_correct = False
        
        # 测试默认值
        unknown_ttl = service._get_data_ttl('unknown_data_type')
        logger.info(f"Unknown data type default TTL: {unknown_ttl}s")
        
        if all_correct:
            logger.info("TTL configuration validation passed!")
        else:
            logger.error("TTL configuration has issues!")
        
        return all_correct
        
    except Exception as e:
        logger.error(f"TTL configuration validation failed: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("Real Scheduling Service Cache Optimization Test")
    print("=" * 60)
    
    # 测试1: TTL配置验证
    print("\nStep 1: TTL Configuration Validation")
    ttl_ok = test_cache_ttl_validation()
    
    if ttl_ok:
        # 测试2: 缓存优化效果
        print("\nStep 2: Cache Optimization Performance Test")
        result = test_cache_optimization()
        
        if result:
            print(f"\nTest Completed! Average cache speedup: {result['avg_speedup']:.1f}x")
        else:
            print("\nCache optimization test failed")
    else:
        print("\nTTL configuration validation failed, skipping subsequent tests")
    
    print("\n" + "=" * 60) 