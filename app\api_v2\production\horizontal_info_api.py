#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
from config.enhanced_config import config

横向信息管理API
提供横向订单信息的查询、存储和汇总功能
"""

import os
import json
import logging
import hashlib
from datetime import datetime
from typing import Dict, Any, List, Optional
from flask import Blueprint, request, jsonify, current_app
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import pymysql

logger = logging.getLogger(__name__)

def get_mysql_session():
    """获取MySQL数据库会话"""
    try:
        engine = create_engine(
            'mysql+pymysql://root:WWWwww123!@localhost:3306/aps',
            charset=config.DB_CHARSET
        )
        Session = sessionmaker(bind=engine)
        return Session()
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def save_horizontal_info(horizontal_data: Dict[str, Any], source_file: str) -> Dict[str, Any]:
    """保存横向信息到数据库"""
    try:
        session = get_mysql_session()
        if not session:
            return {'status': 'error', 'message': '数据库连接失败'}
        
        # 计算文件哈希
        file_hash = ''
        if os.path.exists(source_file):
            with open(source_file, 'rb') as f:
                file_hash = hashlib.md5(f.read()).hexdigest()
            file_size = os.path.getsize(source_file)
        else:
            file_size = 0
        
        # 检查表是否存在，不存在则创建
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS horizontal_order_info (
            id INT AUTO_INCREMENT PRIMARY KEY,
            document_type VARCHAR(100) COMMENT '单据类型',
            document_number VARCHAR(50) UNIQUE NOT NULL COMMENT '单据编号',
            processing_type VARCHAR(100) COMMENT '加工属性',
            order_date VARCHAR(20) COMMENT '下单日期',
            contractor_name VARCHAR(200) COMMENT '加工承揽商名称',
            contractor_contact VARCHAR(100) COMMENT '承揽商联系人',
            contractor_address TEXT COMMENT '承揽商地址',
            contractor_phone VARCHAR(50) COMMENT '承揽商电话',
            contractor_email VARCHAR(100) COMMENT '承揽商Email',
            client_name VARCHAR(200) COMMENT '加工委托方名称',
            client_contact VARCHAR(100) COMMENT '委托方联系人',
            client_address TEXT COMMENT '委托方地址',
            client_phone VARCHAR(50) COMMENT '委托方电话',
            client_email VARCHAR(100) COMMENT '委托方Email',
            source_file VARCHAR(500) COMMENT '源Excel文件路径',
            file_size INT COMMENT '文件大小（字节）',
            file_hash VARCHAR(64) COMMENT '文件MD5哈希值',
            extraction_method VARCHAR(50) DEFAULT 'horizontal_extractor' COMMENT '提取方法',
            extraction_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '提取时间',
            extraction_success_rate FLOAT COMMENT '字段提取成功率',
            related_lots TEXT COMMENT '关联的内部工单号（JSON格式）',
            total_quantity INT COMMENT '总数量',
            total_value FLOAT COMMENT '总金额',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='横向订单信息表';
        """
        
        session.execute(text(create_table_sql))
        session.commit()
        
        # 检查是否已存在
        document_number = horizontal_data.get('单据编号', '')
        if document_number:
            existing = session.execute(
                text("SELECT id FROM horizontal_order_info WHERE document_number = :doc_no"),
                {'doc_no': document_number}
            ).fetchone()
            
            if existing:
                # 更新现有记录
                update_sql = """
                UPDATE horizontal_order_info SET
                    document_type = :document_type,
                    processing_type = :processing_type,
                    order_date = :order_date,
                    contractor_name = :contractor_name,
                    contractor_contact = :contractor_contact,
                    contractor_address = :contractor_address,
                    contractor_phone = :contractor_phone,
                    contractor_email = :contractor_email,
                    client_name = :client_name,
                    source_file = :source_file,
                    file_size = :file_size,
                    file_hash = :file_hash,
                    updated_at = NOW()
                WHERE document_number = :document_number
                """
                
                session.execute(text(update_sql), {
                    'document_type': horizontal_data.get('单据类型', ''),
                    'processing_type': horizontal_data.get('加工属性', ''),
                    'order_date': horizontal_data.get('下单日期', ''),
                    'contractor_name': horizontal_data.get('加工承揽商', ''),
                    'contractor_contact': horizontal_data.get('承揽商联系人', ''),
                    'contractor_address': horizontal_data.get('承揽商地址', ''),
                    'contractor_phone': horizontal_data.get('承揽商电话', ''),
                    'contractor_email': horizontal_data.get('承揽商Email', ''),
                    'client_name': horizontal_data.get('加工委托方', ''),
                    'source_file': source_file,
                    'file_size': file_size,
                    'file_hash': file_hash,
                    'document_number': document_number
                })
                
                action = 'updated'
            else:
                # 插入新记录
                insert_sql = """
                INSERT INTO horizontal_order_info (
                    document_type, document_number, processing_type, order_date,
                    contractor_name, contractor_contact, contractor_address, 
                    contractor_phone, contractor_email, client_name,
                    source_file, file_size, file_hash, extraction_time
                ) VALUES (
                    :document_type, :document_number, :processing_type, :order_date,
                    :contractor_name, :contractor_contact, :contractor_address,
                    :contractor_phone, :contractor_email, :client_name,
                    :source_file, :file_size, :file_hash, NOW()
                )
                """
                
                session.execute(text(insert_sql), {
                    'document_type': horizontal_data.get('单据类型', ''),
                    'document_number': document_number,
                    'processing_type': horizontal_data.get('加工属性', ''),
                    'order_date': horizontal_data.get('下单日期', ''),
                    'contractor_name': horizontal_data.get('加工承揽商', ''),
                    'contractor_contact': horizontal_data.get('承揽商联系人', ''),
                    'contractor_address': horizontal_data.get('承揽商地址', ''),
                    'contractor_phone': horizontal_data.get('承揽商电话', ''),
                    'contractor_email': horizontal_data.get('承揽商Email', ''),
                    'client_name': horizontal_data.get('加工委托方', ''),
                    'source_file': source_file,
                    'file_size': file_size,
                    'file_hash': file_hash
                })
                
                action = 'inserted'
            
            session.commit()
            session.close()
            
            return {
                'status': 'success',
                'message': f'横向信息{action}成功',
                'document_number': document_number,
                'action': action
            }
        else:
            session.close()
            return {
                'status': 'error',
                'message': '缺少单据编号，无法保存'
            }
            
    except Exception as e:
        logger.error(f"保存横向信息失败: {e}")
        return {
            'status': 'error',
            'message': f'保存失败: {str(e)}'
        }

def get_horizontal_info_list(limit: int = 50, offset: int = 0) -> Dict[str, Any]:
    """获取横向信息列表"""
    try:
        session = get_mysql_session()
        if not session:
            return {'status': 'error', 'message': '数据库连接失败'}
        
        # 检查表是否存在
        table_exists = session.execute(
            text("SHOW TABLES LIKE 'horizontal_order_info'")
        ).fetchone()
        
        if not table_exists:
            return {
                'status': 'success',
                'data': [],
                'total': 0,
                'message': '横向信息表不存在'
            }
        
        # 查询数据
        query_sql = """
        SELECT 
            id, document_type, document_number, processing_type, order_date,
            contractor_name, contractor_contact, client_name,
            source_file, extraction_time, created_at
        FROM horizontal_order_info 
        ORDER BY created_at DESC 
        LIMIT :limit OFFSET :offset
        """
        
        count_sql = "SELECT COUNT(*) as total FROM horizontal_order_info"
        
        result = session.execute(text(query_sql), {'limit': limit, 'offset': offset})
        count_result = session.execute(text(count_sql))
        
        data = []
        for row in result:
            data.append({
                'id': row.id,
                'document_type': row.document_type,
                'document_number': row.document_number,
                'processing_type': row.processing_type,
                'order_date': row.order_date,
                'contractor_name': row.contractor_name,
                'contractor_contact': row.contractor_contact,
                'client_name': row.client_name,
                'source_file': os.path.basename(row.source_file) if row.source_file else '',
                'extraction_time': row.extraction_time.isoformat() if row.extraction_time else None,
                'created_at': row.created_at.isoformat() if row.created_at else None
            })
        
        total = count_result.fetchone().total
        session.close()
        
        return {
            'status': 'success',
            'data': data,
            'total': total,
            'limit': limit,
            'offset': offset
        }
        
    except Exception as e:
        logger.error(f"查询横向信息失败: {e}")
        return {
            'status': 'error',
            'message': f'查询失败: {str(e)}'
        }

def get_horizontal_info_by_document(document_number: str) -> Dict[str, Any]:
    """根据单据编号获取横向信息详情"""
    try:
        session = get_mysql_session()
        if not session:
            return {'status': 'error', 'message': '数据库连接失败'}
        
        query_sql = """
        SELECT * FROM horizontal_order_info 
        WHERE document_number = :doc_no
        """
        
        result = session.execute(text(query_sql), {'doc_no': document_number}).fetchone()
        session.close()
        
        if result:
            return {
                'status': 'success',
                'data': {
                    'id': result.id,
                    'document_type': result.document_type,
                    'document_number': result.document_number,
                    'processing_type': result.processing_type,
                    'order_date': result.order_date,
                    'contractor_name': result.contractor_name,
                    'contractor_contact': result.contractor_contact,
                    'contractor_address': result.contractor_address,
                    'contractor_phone': result.contractor_phone,
                    'contractor_email': result.contractor_email,
                    'client_name': result.client_name,
                    'client_contact': result.client_contact,
                    'client_address': result.client_address,
                    'client_phone': result.client_phone,
                    'client_email': result.client_email,
                    'source_file': result.source_file,
                    'file_size': result.file_size,
                    'file_hash': result.file_hash,
                    'extraction_time': result.extraction_time.isoformat() if result.extraction_time else None,
                    'created_at': result.created_at.isoformat() if result.created_at else None,
                    'updated_at': result.updated_at.isoformat() if result.updated_at else None
                }
            }
        else:
            return {
                'status': 'error',
                'message': f'未找到单据编号 {document_number} 的横向信息'
            }
            
    except Exception as e:
        logger.error(f"查询横向信息详情失败: {e}")
        return {
            'status': 'error',
            'message': f'查询失败: {str(e)}'
        }

def get_horizontal_info_summary() -> Dict[str, Any]:
    """获取横向信息汇总统计"""
    try:
        session = get_mysql_session()
        if not session:
            return {'status': 'error', 'message': '数据库连接失败'}
        
        # 检查表是否存在
        table_exists = session.execute(
            text("SHOW TABLES LIKE 'horizontal_order_info'")
        ).fetchone()
        
        if not table_exists:
            return {
                'status': 'success',
                'summary': {
                    'total_documents': 0,
                    'contractors': [],
                    'clients': [],
                    'processing_types': []
                }
            }
        
        # 统计查询
        summary_sql = """
        SELECT 
            COUNT(*) as total_documents,
            COUNT(DISTINCT contractor_name) as unique_contractors,
            COUNT(DISTINCT client_name) as unique_clients,
            COUNT(DISTINCT processing_type) as unique_processing_types
        FROM horizontal_order_info
        WHERE contractor_name IS NOT NULL AND contractor_name != ''
        """
        
        contractors_sql = """
        SELECT contractor_name, COUNT(*) as count
        FROM horizontal_order_info 
        WHERE contractor_name IS NOT NULL AND contractor_name != ''
        GROUP BY contractor_name 
        ORDER BY count DESC
        """
        
        clients_sql = """
        SELECT client_name, COUNT(*) as count
        FROM horizontal_order_info 
        WHERE client_name IS NOT NULL AND client_name != ''
        GROUP BY client_name 
        ORDER BY count DESC
        """
        
        processing_types_sql = """
        SELECT processing_type, COUNT(*) as count
        FROM horizontal_order_info 
        WHERE processing_type IS NOT NULL AND processing_type != ''
        GROUP BY processing_type 
        ORDER BY count DESC
        """
        
        summary_result = session.execute(text(summary_sql)).fetchone()
        contractors_result = session.execute(text(contractors_sql))
        clients_result = session.execute(text(clients_sql))
        processing_types_result = session.execute(text(processing_types_sql))
        
        contractors = [{'name': row.contractor_name, 'count': row.count} for row in contractors_result]
        clients = [{'name': row.client_name, 'count': row.count} for row in clients_result]
        processing_types = [{'type': row.processing_type, 'count': row.count} for row in processing_types_result]
        
        session.close()
        
        return {
            'status': 'success',
            'summary': {
                'total_documents': summary_result.total_documents,
                'unique_contractors': summary_result.unique_contractors,
                'unique_clients': summary_result.unique_clients,
                'unique_processing_types': summary_result.unique_processing_types,
                'contractors': contractors,
                'clients': clients,
                'processing_types': processing_types
            }
        }
        
    except Exception as e:
        logger.error(f"查询横向信息汇总失败: {e}")
        return {
            'status': 'error',
            'message': f'查询失败: {str(e)}'
        } 