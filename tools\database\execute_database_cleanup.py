#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库清理执行脚本
删除确认的废弃表和孤儿表，保护现有生产数据
"""

import os
import sys
import pymysql
import logging
from datetime import datetime
from typing import List, Dict

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('DatabaseCleanup')

class DatabaseCleanupExecutor:
    """数据库清理执行器"""
    
    def __init__(self):
        self.mysql_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'WWWwww123!',
            'charset': 'utf8mb4',
            'database': 'aps'
        }
        
        # 确认要删除的表 - 基于深度代码分析结果
        self.tables_to_delete = {
            # 孤儿表（4个）- 空表且无引用
            'real_time_events': '实时事件表，已废弃',
            'scheduling_decision_history': '排产决策历史，已废弃',
            'wip_lot_backup_20250627_120940': 'WIP备份表，临时文件',
            'v_equipment_real_time_status': '设备实时状态视图，已废弃',
            
            # 功能重复的表（1个）- 用户确认为废弃
            'product_priority_config': '产品优先级配置，功能被lotpriorityconfig替代',
        }
        
        # 可选删除的僵尸表候选（谨慎处理）
        self.zombie_candidates = {
            'task_execution_logs': '任务执行日志，可能是遗留数据（245条）',
            'equipment_digital_twin': '设备数字孪生，可能是实验功能（20条）',
            'equipment_switch_history': '设备切换历史（10条）',
            'product_process_features': '产品工艺特征（7条）',
            'multi_sheets': '多表格数据，可能是测试数据（3条）',
            'normal_file': '普通文件记录（3条）',
            'with_nulls': '空值测试数据（3条）',
            'scheduler_configs': '调度器配置重复表（3条）'
        }

    def get_mysql_connection(self):
        """获取MySQL连接"""
        try:
            return pymysql.connect(**self.mysql_config)
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            return None

    def check_table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        connection = self.get_mysql_connection()
        if not connection:
            return False
        
        try:
            cursor = connection.cursor()
            cursor.execute(f"""
                SELECT COUNT(*) 
                FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = 'aps' AND TABLE_NAME = '{table_name}'
            """)
            result = cursor.fetchone()
            exists = result[0] > 0
            connection.close()
            return exists
        except Exception as e:
            logger.error(f"❌ 检查表 {table_name} 失败: {e}")
            connection.close()
            return False

    def get_table_info(self, table_name: str) -> Dict:
        """获取表的详细信息"""
        connection = self.get_mysql_connection()
        if not connection:
            return {}
        
        try:
            cursor = connection.cursor()
            cursor.execute(f"""
                SELECT TABLE_ROWS, DATA_LENGTH, INDEX_LENGTH
                FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = 'aps' AND TABLE_NAME = '{table_name}'
            """)
            result = cursor.fetchone()
            if result:
                info = {
                    'rows': result[0] or 0,
                    'data_size': result[1] or 0,
                    'index_size': result[2] or 0
                }
            else:
                info = {'rows': 0, 'data_size': 0, 'index_size': 0}
            connection.close()
            return info
        except Exception as e:
            logger.error(f"❌ 获取表 {table_name} 信息失败: {e}")
            connection.close()
            return {}

    def backup_table_structure(self, table_name: str) -> str:
        """备份表结构（CREATE TABLE语句）"""
        connection = self.get_mysql_connection()
        if not connection:
            return ""
        
        try:
            cursor = connection.cursor()
            cursor.execute(f"SHOW CREATE TABLE {table_name}")
            result = cursor.fetchone()
            create_sql = result[1] if result else ""
            connection.close()
            return create_sql
        except Exception as e:
            logger.warning(f"⚠️ 备份表 {table_name} 结构失败: {e}")
            connection.close()
            return ""

    def delete_table(self, table_name: str) -> bool:
        """删除表"""
        connection = self.get_mysql_connection()
        if not connection:
            return False
        
        try:
            cursor = connection.cursor()
            cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
            connection.commit()
            connection.close()
            return True
        except Exception as e:
            logger.error(f"❌ 删除表 {table_name} 失败: {e}")
            connection.close()
            return False

    def execute_cleanup(self, include_zombies: bool = False) -> Dict:
        """执行清理操作"""
        logger.info("🧹 开始执行数据库清理操作...")
        
        results = {
            'deleted_tables': [],
            'not_found_tables': [],
            'failed_tables': [],
            'backup_info': []
        }
        
        # 确定要处理的表列表
        tables_to_process = self.tables_to_delete.copy()
        if include_zombies:
            tables_to_process.update(self.zombie_candidates)
        
        logger.info(f"📋 计划处理 {len(tables_to_process)} 个表")
        
        # 创建备份目录
        backup_dir = f"database_cleanup_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(backup_dir, exist_ok=True)
        
        for table_name, description in tables_to_process.items():
            logger.info(f"🔍 处理表: {table_name}")
            
            # 检查表是否存在
            if not self.check_table_exists(table_name):
                logger.info(f"   ⚪ 表不存在: {table_name}")
                results['not_found_tables'].append(table_name)
                continue
            
            # 获取表信息
            table_info = self.get_table_info(table_name)
            logger.info(f"   📊 表信息: {table_info['rows']}条记录, {table_info['data_size']}字节")
            
            # 备份表结构
            create_sql = self.backup_table_structure(table_name)
            if create_sql:
                backup_file = os.path.join(backup_dir, f"{table_name}_structure.sql")
                with open(backup_file, 'w', encoding='utf-8') as f:
                    f.write(f"-- 表 {table_name} 的备份结构\n")
                    f.write(f"-- 描述: {description}\n")
                    f.write(f"-- 备份时间: {datetime.now()}\n")
                    f.write(f"-- 表信息: {table_info}\n\n")
                    f.write(create_sql + ";\n")
                results['backup_info'].append(backup_file)
                logger.info(f"   💾 结构已备份到: {backup_file}")
            
            # 删除表
            if self.delete_table(table_name):
                logger.info(f"   ✅ 成功删除表: {table_name}")
                results['deleted_tables'].append(table_name)
            else:
                logger.error(f"   ❌ 删除表失败: {table_name}")
                results['failed_tables'].append(table_name)
        
        return results

    def generate_cleanup_report(self, results: Dict) -> str:
        """生成清理报告"""
        report = []
        report.append("🧹 数据库清理操作报告")
        report.append("=" * 60)
        report.append(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("=" * 60)
        
        # 成功删除的表
        if results['deleted_tables']:
            report.append(f"\n✅ 成功删除的表 ({len(results['deleted_tables'])}个):")
            for table in results['deleted_tables']:
                description = self.tables_to_delete.get(table, self.zombie_candidates.get(table, ''))
                report.append(f"   🗑️ {table} - {description}")
        
        # 不存在的表
        if results['not_found_tables']:
            report.append(f"\n⚪ 不存在的表 ({len(results['not_found_tables'])}个):")
            for table in results['not_found_tables']:
                report.append(f"   ❓ {table}")
        
        # 删除失败的表
        if results['failed_tables']:
            report.append(f"\n❌ 删除失败的表 ({len(results['failed_tables'])}个):")
            for table in results['failed_tables']:
                report.append(f"   🚨 {table}")
        
        # 备份信息
        if results['backup_info']:
            report.append(f"\n💾 备份文件 ({len(results['backup_info'])}个):")
            for backup_file in results['backup_info']:
                report.append(f"   📁 {backup_file}")
        
        # 总结
        total_processed = len(results['deleted_tables']) + len(results['not_found_tables']) + len(results['failed_tables'])
        report.append(f"\n📊 操作总结:")
        report.append(f"   📋 计划处理: {total_processed}个表")
        report.append(f"   ✅ 成功删除: {len(results['deleted_tables'])}个")
        report.append(f"   ⚪ 表不存在: {len(results['not_found_tables'])}个")
        report.append(f"   ❌ 删除失败: {len(results['failed_tables'])}个")
        
        return "\n".join(report)

    def run_safe_cleanup(self):
        """运行安全清理（仅删除确认的废弃表）"""
        logger.info("🛡️ 执行安全数据库清理...")
        
        print("🧹 APS 数据库安全清理工具")
        print("=" * 50)
        print("\n📋 计划删除的表:")
        
        for table_name, description in self.tables_to_delete.items():
            # 检查表是否存在并获取信息
            if self.check_table_exists(table_name):
                info = self.get_table_info(table_name)
                status = f"({info['rows']}条记录)" if info['rows'] > 0 else "(空表)"
            else:
                status = "(不存在)"
            print(f"   🗑️ {table_name} - {description} {status}")
        
        print(f"\n⚠️ 僵尸表候选（可选删除）:")
        for table_name, description in self.zombie_candidates.items():
            if self.check_table_exists(table_name):
                info = self.get_table_info(table_name)
                status = f"({info['rows']}条记录)"
            else:
                status = "(不存在)"
            print(f"   ⚠️ {table_name} - {description} {status}")
        
        print("\n选择清理模式:")
        print("1. 安全模式 - 仅删除确认的废弃表（推荐）")
        print("2. 完全模式 - 包括僵尸表候选（高风险）")
        print("3. 取消操作")
        
        choice = input("\n请选择 (1/2/3): ").strip()
        
        if choice == '1':
            # 安全模式
            results = self.execute_cleanup(include_zombies=False)
        elif choice == '2':
            # 完全模式
            confirm = input("⚠️ 确认执行完全清理？这将删除所有僵尸表候选！(yes/no): ").strip().lower()
            if confirm == 'yes':
                results = self.execute_cleanup(include_zombies=True)
            else:
                print("❌ 操作已取消")
                return
        else:
            print("❌ 操作已取消")
            return
        
        # 生成并显示报告
        report = self.generate_cleanup_report(results)
        print("\n" + report)
        
        # 保存报告到文件
        report_file = f"database_cleanup_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"📝 清理报告已保存到: {report_file}")
        
        return results

def main():
    """主函数"""
    executor = DatabaseCleanupExecutor()
    return executor.run_safe_cleanup()

if __name__ == "__main__":
    main() 