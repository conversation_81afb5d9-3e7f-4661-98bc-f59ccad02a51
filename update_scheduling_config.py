#!/usr/bin/env python3
"""
排产配置更新工具
用于动态修改排产系统的配置参数
"""

import os
import sys
import json
from typing import Dict, Any

def get_current_config() -> Dict[str, Any]:
    """获取当前配置"""
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from app.config.scheduling_config import SchedulingConfig
        
        return {
            'duplicate_check': {
                'manual_scheduling_tolerance': SchedulingConfig.get_manual_scheduling_tolerance(),
                'scheduled_task_tolerance': SchedulingConfig.get_scheduled_task_tolerance(),
                'enable_duplicate_check': SchedulingConfig.is_duplicate_check_enabled(),
                'strict_mode': SchedulingConfig.is_strict_mode_enabled()
            },
            'lock_config': {
                'lock_timeout': SchedulingConfig.get_lock_timeout()
            }
        }
    except Exception as e:
        print(f"❌ 获取配置失败: {e}")
        return {}

def update_config(config_updates: Dict[str, Any]) -> bool:
    """更新配置文件"""
    try:
        config_file_path = os.path.join(os.path.dirname(__file__), 'app', 'config', 'scheduling_config.py')
        
        # 读取当前配置文件
        with open(config_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新配置值
        for section, updates in config_updates.items():
            if section == 'duplicate_check':
                for key, value in updates.items():
                    if key == 'manual_scheduling_tolerance':
                        content = content.replace(
                            "'MANUAL_SCHEDULING_TOLERANCE': 30",
                            f"'MANUAL_SCHEDULING_TOLERANCE': {value}"
                        )
                    elif key == 'scheduled_task_tolerance':
                        content = content.replace(
                            "'SCHEDULED_TASK_TOLERANCE': 30",
                            f"'SCHEDULED_TASK_TOLERANCE': {value}"
                        )
                    elif key == 'enable_duplicate_check':
                        content = content.replace(
                            "'ENABLE_DUPLICATE_CHECK': True",
                            f"'ENABLE_DUPLICATE_CHECK': {value}"
                        )
                    elif key == 'strict_mode':
                        content = content.replace(
                            "'STRICT_MODE': True",
                            f"'STRICT_MODE': {value}"
                        )
            elif section == 'lock_config':
                for key, value in updates.items():
                    if key == 'lock_timeout':
                        content = content.replace(
                            "'LOCK_TIMEOUT': 300",
                            f"'LOCK_TIMEOUT': {value}"
                        )
        
        # 写回配置文件
        with open(config_file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 配置文件更新成功")
        return True
        
    except Exception as e:
        print(f"❌ 更新配置失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 排产系统配置管理工具")
    print("=" * 50)
    
    # 显示当前配置
    current_config = get_current_config()
    if current_config:
        print("\n📋 当前配置:")
        print(json.dumps(current_config, indent=2, ensure_ascii=False))
    
    print("\n🛠️ 可用的配置选项:")
    print("1. 修改手动排产重复检测间隔")
    print("2. 修改定时任务排产重复检测间隔")
    print("3. 启用/禁用重复检测")
    print("4. 修改排产锁超时时间")
    print("5. 批量更新配置")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请选择操作 (0-5): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                seconds = int(input("请输入手动排产重复检测间隔（秒，当前30秒）: "))
                if update_config({'duplicate_check': {'manual_scheduling_tolerance': seconds}}):
                    print(f"✅ 手动排产重复检测间隔已更新为 {seconds} 秒")
            elif choice == '2':
                seconds = int(input("请输入定时任务排产重复检测间隔（秒，当前30秒）: "))
                if update_config({'duplicate_check': {'scheduled_task_tolerance': seconds}}):
                    print(f"✅ 定时任务排产重复检测间隔已更新为 {seconds} 秒")
            elif choice == '3':
                enable = input("是否启用重复检测？(y/n): ").strip().lower() == 'y'
                if update_config({'duplicate_check': {'enable_duplicate_check': enable}}):
                    status = "启用" if enable else "禁用"
                    print(f"✅ 重复检测已{status}")
            elif choice == '4':
                seconds = int(input("请输入排产锁超时时间（秒，当前300秒）: "))
                if update_config({'lock_config': {'lock_timeout': seconds}}):
                    print(f"✅ 排产锁超时时间已更新为 {seconds} 秒")
            elif choice == '5':
                print("\n📝 批量配置更新:")
                manual_tolerance = int(input("手动排产重复检测间隔（秒）: "))
                task_tolerance = int(input("定时任务排产重复检测间隔（秒）: "))
                enable_check = input("启用重复检测？(y/n): ").strip().lower() == 'y'
                lock_timeout = int(input("排产锁超时时间（秒）: "))
                
                config_updates = {
                    'duplicate_check': {
                        'manual_scheduling_tolerance': manual_tolerance,
                        'scheduled_task_tolerance': task_tolerance,
                        'enable_duplicate_check': enable_check
                    },
                    'lock_config': {
                        'lock_timeout': lock_timeout
                    }
                }
                
                if update_config(config_updates):
                    print("✅ 批量配置更新成功")
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n👋 操作已取消")
            break
        except ValueError:
            print("❌ 输入格式错误，请输入数字")
        except Exception as e:
            print(f"❌ 操作失败: {e}")
    
    print("\n💡 提示：配置更新后需要重启应用才能生效")

if __name__ == "__main__":
    main()
