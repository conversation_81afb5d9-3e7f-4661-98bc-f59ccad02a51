{% extends "base.html" %}

{% set page_title = "排产失败清单" %}
{% set table_title = "排产失败清单" %}
{% set page_description = "查看无法排产的批次及失败原因，便于问题分析和数据修复" %}

{% block title %}{{ page_title }} - AEC-FT ICP{% endblock %}

{% block extra_css %}
<!-- 引入Bootstrap和FontAwesome -->
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/bootstrap/bootstrap.min.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/all.min.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/custom/theme.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/custom/production-tables.css') }}">

<style>
/* 失败批次页面特有样式 - 映射到统一样式系统 */
.failure-reason-high { 
    background-color: var(--aps-status-danger); 
    color: white; 
    padding: 0.2rem 0.5rem; 
    border-radius: 0.25rem; 
    font-size: 0.7rem; 
    font-weight: 500; 
    text-transform: uppercase; 
    letter-spacing: 0.5px; 
}
.failure-reason-medium { 
    background-color: var(--aps-status-warning); 
    color: #212529; 
    padding: 0.2rem 0.5rem; 
    border-radius: 0.25rem; 
    font-size: 0.7rem; 
    font-weight: 500; 
    text-transform: uppercase; 
    letter-spacing: 0.5px; 
}
.failure-reason-low { 
    background-color: var(--aps-status-info); 
    color: white; 
    padding: 0.2rem 0.5rem; 
    border-radius: 0.25rem; 
    font-size: 0.7rem; 
    font-weight: 500; 
    text-transform: uppercase; 
    letter-spacing: 0.5px; 
}

/* 列样式使用统一系统 */
.lot-id-column {
    font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
    font-weight: bold;
    color: var(--aps-status-danger);
    font-size: 0.75rem;
}
.quantity-column { 
    text-align: right; 
    font-weight: bold; 
    font-family: 'Monaco', 'Consolas', 'Courier New', monospace; 
}
.suggestion-column { 
    font-size: 0.75rem; 
    color: #6c757d; 
    max-width: 200px;
    word-break: break-word;
    line-height: 1.3;
}

.loading-overlay {
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.table-container {
    position: relative;
    min-height: 400px;
}

.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
}

.card-header {
    background: linear-gradient(135deg, #b72424 0%, #b72424 100%);
    color: rgb(255, 255, 255);
    font-weight: bold;
}

/* 使用统一表格样式，移除重复定义 */

.btn-group-sm > .btn, .btn-sm {
    padding: 0.15rem 0.3rem;
    font-size: 0.7rem;
}

.stats-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 6px;
    padding: 0.6rem;
    text-align: center;
    border: 1px solid #dee2e6;
}

.stats-number {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.15rem;
}

.stats-label {
    color: #6c757d;
    font-size: 0.8rem;
    margin: 0;
}

/* 表格排序功能样式 */
.table-sortable th.sortable {
    position: relative;
    user-select: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.table-sortable th.sortable:hover {
    background-color: #e9ecef;
}

.table-sortable th.no-sort {
    cursor: default;
}

.table-sortable th.no-sort:hover {
    background-color: #f8f9fa;
}

/* 排序图标样式 */
.sort-icon {
    transition: all 0.2s ease;
    font-size: 0.75rem;
    margin-left: 0.25rem;
}

.table-sortable th.sorted-asc .sort-icon {
    color: #dc3545 !important;
}

.table-sortable th.sorted-desc .sort-icon {
    color: #dc3545 !important;
}

.table-sortable th.sorted-asc .sort-icon::before {
    content: "\f0de"; /* fa-sort-up */
}

.table-sortable th.sorted-desc .sort-icon::before {
    content: "\f0dd"; /* fa-sort-down */
}

/* 紧凑表格样式 */
.compact-table th,
.compact-table td {
    padding: 0.25rem 0.35rem;
    font-size: 0.75rem;
    line-height: 1.2;
    border-width: 1px;
}

.compact-table .badge {
    font-size: 0.7rem;
    padding: 0.15rem 0.3rem;
}

.compact-table .btn-sm {
    font-size: 0.7rem;
    padding: 0.15rem 0.3rem;
}

/* 时间样式使用统一系统 */
.time-value { 
    font-family: 'Monaco', 'Consolas', 'Courier New', monospace; 
    font-size: 0.7rem; 
    color: #495057; 
}
.datetime-value { 
    font-size: 0.7rem; 
    color: #6c757d; 
    white-space: nowrap; 
}

/* 数据源信息样式 */
.data-source-info {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 4px;
    padding: 8px 12px;
    margin-bottom: 1rem;
    font-size: 0.85rem;
    color: #1565c0;
}

.data-source-info .badge {
    margin-right: 0.5rem;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

/* 批次类型样式 */
.lot-type-badge {
    background: #e3f2fd;
    color: #1565c0;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.7rem;
    font-weight: 500;
}

/* 封装形式样式 */
.pkg-pn-badge {
    background: #f3e5f5;
    color: #6a1b9a;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.7rem;
    font-weight: 500;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题和操作栏 -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h1 class="h4 mb-1">
                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                {{ page_title }}
            </h1>
            <p class="text-muted mb-0">{{ page_description }}</p>
        </div>
        
        <!-- 操作按钮 -->
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-primary btn-sm" onclick="retryScheduling()" title="重新排产">
                <i class="fas fa-redo me-1"></i>当前排产
            </button>
            <button type="button" class="btn btn-secondary btn-sm" onclick="viewHistory()" title="历史累计">
                <i class="fas fa-history me-1"></i>历史累计
            </button>
            <button type="button" class="btn btn-success btn-sm" onclick="refreshData()" title="刷新数据">
                <i class="fas fa-sync-alt me-1"></i>刷新数据
            </button>
            <button type="button" class="btn btn-info btn-sm" onclick="exportCSV()" title="导出清单">
                <i class="fas fa-download me-1"></i>导出清单
            </button>
            <button type="button" class="btn btn-danger btn-sm" onclick="clearAllFailed()" title="清空清单">
                <i class="fas fa-trash me-1"></i>清空清单
            </button>
        </div>
    </div>
    
    <!-- 数据库连接信息 -->
    <div id="dataSourceInfo" class="data-source-info" style="display: none;">
        <span class="badge bg-success">database</span>
        <strong>数据库:</strong> <span id="dataSource">-</span> | 
        <strong>数据表:</strong> <span id="dataTable">-</span> | 
        <strong>失败记录总数:</strong> <span id="failureCount">-</span> | 
        <strong>失败类型统计:</strong> <span id="failureTypes">-</span>
    </div>
    
    <!-- 统计卡片 -->
    <div class="row mb-3" id="statsContainer">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number text-warning" id="totalFailures">97</div>
                <p class="stats-label">失败批次总数</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number text-danger" id="configMissing">0</div>
                <p class="stats-label">配置缺失</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number text-secondary" id="equipmentIncompatible">97</div>
                <p class="stats-label">设备不兼容</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number text-info" id="otherReasons">0</div>
                <p class="stats-label">其他原因</p>
            </div>
        </div>
    </div>
    
    <!-- 主内容区域 -->
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">
                        <i class="fas fa-list-alt me-2"></i>失败批次详情
                        <span class="badge bg-light text-dark ms-2" id="failureTypeCount">97</span>
                    </h5>
                </div>
                <div class="col-auto">
                    <small class="text-light">查看无法排产的批次及失败原因，便于问题分析和数据修复</small>
                </div>
            </div>
        </div>
        
        <div class="card-body p-0">
            <!-- 搜索工具栏 - 参考已调度批次管理 -->
            <div class="card mb-3 mx-3 mt-3">
                <div class="card-body py-2">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <div class="input-group input-group-sm">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="searchInput" placeholder="搜索工单号、产品名称、失败原因、工序、封装形式...">
                                <button class="btn btn-outline-secondary" type="button" id="clearSearchBtn" title="清空搜索">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <!-- 搜索提示 -->
                            <small class="text-muted" id="searchHint" style="display: none;">
                                <i class="fas fa-info-circle"></i> 支持搜索：工单号、产品、失败原因、工序、封装等
                            </small>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select form-select-sm" id="stageFilter">
                                <option value="">全部工序</option>
                                <!-- 动态加载选项 -->
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select form-select-sm" id="failureTypeFilter">
                                <option value="">全部失败类型</option>
                                <!-- 动态加载选项 -->
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select form-select-sm" id="lotTypeFilter">
                                <option value="">全部工单分类</option>
                                <!-- 动态加载选项 -->
                            </select>
                        </div>
                    </div>
                    
                    <!-- 筛选状态栏 -->
                    <div class="row mt-2">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center">
                                <button type="button" class="btn btn-outline-secondary btn-sm me-2" id="resetFiltersBtn" title="重置所有筛选条件">
                                    <i class="fas fa-undo"></i> 重置筛选
                                </button>
                                <span class="text-muted" id="filterStatus">
                                    <i class="fas fa-filter"></i> 无筛选条件
                                </span>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-info" id="resultCount">显示 0 条记录</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 数据表格 -->
            <div class="table-container mx-3">
                <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                    <div class="spinner-border text-danger" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
                
                <div class="aps-table-responsive">
<table class="aps-table table-hover table-sm table-sortable compact-table" id="failedLotsTable">
                        <thead>
                            <tr id="tableHeaders">
                                <th class="sortable" data-column="LOT_ID">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <span>内部工单号</span>
                                        <i class="fas fa-sort text-muted sort-icon"></i>
                                    </div>
                                </th>
                                <th class="sortable" data-column="STAGE">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <span>工序</span>
                                        <i class="fas fa-sort text-muted sort-icon"></i>
                                    </div>
                                </th>
                                <th class="sortable" data-column="DEVICE">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <span>产品名称</span>
                                        <i class="fas fa-sort text-muted sort-icon"></i>
                                    </div>
                                </th>
                                <th class="sortable" data-column="LOT_TYPE">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <span>工单分类</span>
                                        <i class="fas fa-sort text-muted sort-icon"></i>
                                    </div>
                                </th>
                                <th class="sortable" data-column="PKG_PN">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <span>封装形式</span>
                                        <i class="fas fa-sort text-muted sort-icon"></i>
                                    </div>
                                </th>
                                <th class="sortable" data-column="GOOD_QTY">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <span>良品数量</span>
                                        <i class="fas fa-sort text-muted sort-icon"></i>
                                    </div>
                                </th>
                                <th class="sortable" data-column="failure_reason">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <span>失败原因</span>
                                        <i class="fas fa-sort text-muted sort-icon"></i>
                                    </div>
                                </th>
                                <th class="sortable" data-column="suggestion">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <span>建议解决方案</span>
                                        <i class="fas fa-sort text-muted sort-icon"></i>
                                    </div>
                                </th>
                                <th class="sortable" data-column="timestamp">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <span>创建时间</span>
                                        <i class="fas fa-sort text-muted sort-icon"></i>
                                    </div>
                                </th>
                                <th width="100" class="no-sort">操作</th>
                            </tr>
                        </thead>
                        <tbody id="failedLotsTableBody">
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <div class="spinner-border text-danger" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2 text-muted mb-0">正在加载失败批次数据...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 分页导航 -->
            <div class="d-flex justify-content-between align-items-center mt-3 mx-3 pb-3">
                <div>
                    <select class="form-select form-select-sm" style="width: auto;" id="pageSize" onchange="changePageSize()">
                        <option value="25">25 条/页</option>
                        <option value="50" selected>50 条/页</option>
                        <option value="100">100 条/页</option>
                    </select>
                </div>
                <nav aria-label="数据分页">
                    <ul class="pagination pagination-sm mb-0" id="pagination">
                        <!-- 分页按钮动态生成 -->
                    </ul>
                </nav>
                <div>
                    <small class="text-muted">
                        显示第 <span id="startRecord">0</span> - <span id="endRecord">0</span> 条，
                        共 <span id="totalCount">0</span> 条记录
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 引入Bootstrap JS -->
<script src="{{ url_for('static', filename='vendor/bootstrap/bootstrap.bundle.min.js') }}"></script>

<script>
// 全局变量
let allFailedLots = [];
let filteredLots = [];
let currentPage = 1;
let pageSize = 50;
let isCurrentOnly = true; // 默认查看当前排产会话
let sortColumn = 'timestamp';
let sortDirection = 'desc';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔥 失败批次页面初始化...');
    initializeEventListeners();
    initializeSorting();
    // 先加载数据，然后在数据加载完成后生成筛选选项
    loadFailedLots();
});

// 初始化事件监听器
function initializeEventListeners() {
    // 搜索框事件
    const searchInput = document.getElementById('searchInput');
    searchInput.addEventListener('input', debounce(filterLots, 300));
    searchInput.addEventListener('focus', () => {
        document.getElementById('searchHint').style.display = 'block';
    });
    searchInput.addEventListener('blur', () => {
        setTimeout(() => {
            document.getElementById('searchHint').style.display = 'none';
        }, 3000);
    });
    
    document.getElementById('clearSearchBtn').addEventListener('click', clearSearch);
    
    // 筛选下拉框事件
    document.getElementById('stageFilter').addEventListener('change', filterLots);
    document.getElementById('failureTypeFilter').addEventListener('change', filterLots);
    document.getElementById('lotTypeFilter').addEventListener('change', filterLots);
    
    // 重置筛选按钮事件
    document.getElementById('resetFiltersBtn').addEventListener('click', resetAllFilters);
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 加载筛选选项
async function loadFilterOptions() {
    try {
        console.log('🔍 加载筛选选项...');
        
        // 🔧 临时解决方案：基于现有数据动态生成筛选选项
        if (allFailedLots && allFailedLots.length > 0) {
            updateFilterOptionsFromData(allFailedLots);
            return;
        }
        
        // 尝试从API加载
        try {
            const response = await fetch('/api/v2/production/get-failed-lots-filter-options');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            console.log('📊 筛选选项API响应:', result);
            
            if (result.success) {
                const data = result.data;
                
                // 更新工序筛选选项
                const stageFilter = document.getElementById('stageFilter');
                stageFilter.innerHTML = '<option value="">全部工序</option>';
                data.stages.forEach(stage => {
                    const option = document.createElement('option');
                    option.value = stage.value;
                    option.textContent = stage.label;
                    stageFilter.appendChild(option);
                });
                
                // 更新工单分类筛选选项
                const lotTypeFilter = document.getElementById('lotTypeFilter');
                lotTypeFilter.innerHTML = '<option value="">全部工单分类</option>';
                data.lot_types.forEach(lotType => {
                    const option = document.createElement('option');
                    option.value = lotType.value;
                    option.textContent = lotType.label;
                    lotTypeFilter.appendChild(option);
                });
                
                // 更新失败类型筛选选项
                const failureTypeFilter = document.getElementById('failureTypeFilter');
                failureTypeFilter.innerHTML = '<option value="">全部失败类型</option>';
                data.failure_types.forEach(failureType => {
                    const option = document.createElement('option');
                    option.value = failureType.value;
                    option.textContent = failureType.label;
                    failureTypeFilter.appendChild(option);
                });
                
                console.log(`✅ 成功从API加载筛选选项: 工序(${data.stages.length}), 工单分类(${data.lot_types.length}), 失败类型(${data.failure_types.length})`);
            } else {
                throw new Error(result.message || '获取筛选选项失败');
            }
        } catch (apiError) {
            console.warn('⚠️ API加载失败，使用数据生成筛选选项:', apiError.message);
            // 等待数据加载完成后再生成选项
            setTimeout(() => {
                if (allFailedLots && allFailedLots.length > 0) {
                    updateFilterOptionsFromData(allFailedLots);
                }
            }, 1000);
        }
    } catch (error) {
        console.error('❌ 加载筛选选项失败:', error);
        // 如果加载失败，保持默认选项
    }
}

// 💡 新增：基于实际数据生成筛选选项
function updateFilterOptionsFromData(failedLots) {
    try {
        console.log('🔍 基于数据生成筛选选项...');
        
        // 提取所有不同的工序
        const stages = [...new Set(failedLots.filter(lot => lot.STAGE).map(lot => lot.STAGE))].sort();
        
        // 提取所有不同的工单分类
        const lotTypes = [...new Set(failedLots.filter(lot => lot.LOT_TYPE).map(lot => lot.LOT_TYPE))].sort();
        
        // 更新工序筛选选项
        const stageFilter = document.getElementById('stageFilter');
        stageFilter.innerHTML = '<option value="">全部工序</option>';
        stages.forEach(stage => {
            const option = document.createElement('option');
            option.value = stage;
            option.textContent = stage;
            stageFilter.appendChild(option);
        });
        
        // 更新工单分类筛选选项
        const lotTypeFilter = document.getElementById('lotTypeFilter');
        lotTypeFilter.innerHTML = '<option value="">全部工单分类</option>';
        lotTypes.forEach(lotType => {
            const option = document.createElement('option');
            option.value = lotType;
            option.textContent = lotType;
            lotTypeFilter.appendChild(option);
        });
        
        // 更新失败类型筛选选项（基于失败原因分析）
        const failureTypeFilter = document.getElementById('failureTypeFilter');
        failureTypeFilter.innerHTML = '<option value="">全部失败类型</option>';
        
        // 分析失败原因并生成分类
        const hasConfigIssues = failedLots.some(lot => {
            const reason = lot.failure_reason || '';
            return reason.includes('配置') || reason.includes('规范') || reason.includes('config') || reason.includes('spec');
        });
        
        const hasEquipmentIssues = failedLots.some(lot => {
            const reason = lot.failure_reason || '';
            return reason.includes('设备') || reason.includes('不兼容') || reason.includes('无合适') || 
                   reason.includes('equipment') || reason.includes('incompatible');
        });
        
        if (hasConfigIssues) {
            const option = document.createElement('option');
            option.value = '配置';
            option.textContent = '配置缺失';
            failureTypeFilter.appendChild(option);
        }
        
        if (hasEquipmentIssues) {
            const option = document.createElement('option');
            option.value = '设备';
            option.textContent = '设备不兼容';
            failureTypeFilter.appendChild(option);
        }
        
        // 添加其他类型选项
        const option = document.createElement('option');
        option.value = '其他';
        option.textContent = '其他原因';
        failureTypeFilter.appendChild(option);
        
        console.log(`✅ 成功基于数据生成筛选选项: 工序(${stages.length}), 工单分类(${lotTypes.length}), 失败类型(3)`);
        
    } catch (error) {
        console.error('❌ 基于数据生成筛选选项失败:', error);
    }
}

// 加载失败批次数据
async function loadFailedLots() {
    try {
        showLoading();
        
        const url = `/api/v2/production/get-failed-lots-from-logs?current_only=${isCurrentOnly}`;
        console.log(`🔍 请求失败批次数据: ${url}`);
        
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        console.log('📊 失败批次API响应:', result);
        
        if (result.success) {
            // 适配新的数据结构
            allFailedLots = result.data.failed_lots || [];
            
            // 更新统计信息
            if (result.data.summary) {
                updateStats(result.data.summary);
            }
            
            // 更新数据源信息
            if (result.debug_info) {
                updateDataSourceInfo(result.debug_info);
            }
            
            // 💡 在数据加载完成后生成筛选选项
            updateFilterOptionsFromData(allFailedLots);
            
            // 应用筛选和渲染
            filterLots();
            
            console.log(`✅ 成功加载 ${allFailedLots.length} 条失败批次记录`);
        } else {
            throw new Error(result.message || '获取失败批次数据失败');
        }
    } catch (error) {
        console.error('❌ 加载失败批次数据出错:', error);
        showError('加载失败批次数据失败: ' + error.message);
    } finally {
        hideLoading();
    }
}

// 更新统计信息
function updateStats(summary) {
    document.getElementById('totalFailures').textContent = summary.total_failed || 0;
    document.getElementById('configMissing').textContent = summary.config_missing || 0;
    document.getElementById('equipmentIncompatible').textContent = summary.equipment_incompatible || 0;
    document.getElementById('otherReasons').textContent = summary.other_reasons || 0;
    document.getElementById('failureTypeCount').textContent = summary.total_failed || 0;
}

// 更新数据源信息
function updateDataSourceInfo(debugInfo) {
    if (debugInfo) {
        document.getElementById('dataSource').textContent = debugInfo.data_source || 'unknown';
        document.getElementById('dataTable').textContent = debugInfo.table_name || 'scheduling_failed_lots';
        document.getElementById('failureCount').textContent = debugInfo.record_count || 0;
        document.getElementById('failureTypes').textContent = debugInfo.failure_types || '配置缺失、设备不兼容等';
        document.getElementById('dataSourceInfo').style.display = 'block';
    }
}

// 筛选批次数据
function filterLots() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
    const stageFilter = document.getElementById('stageFilter').value;
    const failureTypeFilter = document.getElementById('failureTypeFilter').value;
    const lotTypeFilter = document.getElementById('lotTypeFilter').value;
    
    filteredLots = allFailedLots.filter(lot => {
        // 💡 增强搜索条件 - 支持多字段模糊匹配
        const matchSearch = !searchTerm || 
            // 工单号匹配
            (lot.LOT_ID && lot.LOT_ID.toLowerCase().includes(searchTerm)) ||
            // 产品名称匹配
            (lot.DEVICE && lot.DEVICE.toLowerCase().includes(searchTerm)) ||
            // 失败原因匹配
            (lot.failure_reason && lot.failure_reason.toLowerCase().includes(searchTerm)) ||
            // 建议解决方案匹配
            (lot.suggestion && lot.suggestion.toLowerCase().includes(searchTerm)) ||
            // 封装形式匹配
            (lot.PKG_PN && lot.PKG_PN.toLowerCase().includes(searchTerm)) ||
            // 工序匹配
            (lot.STAGE && lot.STAGE.toLowerCase().includes(searchTerm)) ||
            // 工单分类匹配
            (lot.LOT_TYPE && lot.LOT_TYPE.toLowerCase().includes(searchTerm));
        
        // 🔍 精确工序筛选
        const matchStage = !stageFilter || lot.STAGE === stageFilter;
        
        // 🔍 增强失败类型筛选逻辑
        const matchFailureType = !failureTypeFilter || 
            (failureTypeFilter === '配置' && lot.failure_reason && (
                lot.failure_reason.includes('配置') || 
                lot.failure_reason.includes('规范') ||
                lot.failure_reason.includes('config') ||
                lot.failure_reason.includes('spec')
            )) ||
            (failureTypeFilter === '设备' && lot.failure_reason && (
                lot.failure_reason.includes('设备') || 
                lot.failure_reason.includes('不兼容') ||
                lot.failure_reason.includes('equipment') ||
                lot.failure_reason.includes('incompatible') ||
                lot.failure_reason.includes('无合适')
            )) ||
            (failureTypeFilter === '其他' && lot.failure_reason && 
                !lot.failure_reason.includes('配置') && 
                !lot.failure_reason.includes('规范') &&
                !lot.failure_reason.includes('设备') && 
                !lot.failure_reason.includes('不兼容') &&
                !lot.failure_reason.includes('无合适') &&
                !lot.failure_reason.includes('config') &&
                !lot.failure_reason.includes('spec') &&
                !lot.failure_reason.includes('equipment') &&
                !lot.failure_reason.includes('incompatible')
            );
        
        // 🔍 精确工单分类筛选
        const matchLotType = !lotTypeFilter || lot.LOT_TYPE === lotTypeFilter;
        
        return matchSearch && matchStage && matchFailureType && matchLotType;
    });
    
    // 排序
    sortLots();
    
    // 重置到第一页
    currentPage = 1;
    
    // 渲染表格
    renderFailedLots();
    
    // 更新分页
    updatePagination();
    
    // 💡 更新搜索结果提示和状态
    console.log(`🔍 筛选完成: 从${allFailedLots.length}条记录中筛选出${filteredLots.length}条`);
    updateFilterStatus();
}

// 更新筛选状态显示
function updateFilterStatus() {
    const searchTerm = document.getElementById('searchInput').value.trim();
    const stageFilter = document.getElementById('stageFilter').value;
    const failureTypeFilter = document.getElementById('failureTypeFilter').value;
    const lotTypeFilter = document.getElementById('lotTypeFilter').value;
    
    const filterStatus = document.getElementById('filterStatus');
    const resultCount = document.getElementById('resultCount');
    
    // 更新结果数量
    resultCount.textContent = `显示 ${filteredLots.length} / ${allFailedLots.length} 条记录`;
    resultCount.className = filteredLots.length === allFailedLots.length ? 'badge bg-info' : 'badge bg-warning';
    
    // 生成筛选状态描述
    const activeFilters = [];
    if (searchTerm) activeFilters.push(`搜索"${searchTerm}"`);
    if (stageFilter) activeFilters.push(`工序"${stageFilter}"`);
    if (failureTypeFilter) activeFilters.push(`失败类型"${failureTypeFilter}"`);
    if (lotTypeFilter) activeFilters.push(`工单分类"${lotTypeFilter}"`);
    
    if (activeFilters.length > 0) {
        filterStatus.innerHTML = `<i class="fas fa-filter text-primary"></i> 已筛选: ${activeFilters.join(', ')}`;
    } else {
        filterStatus.innerHTML = `<i class="fas fa-filter"></i> 无筛选条件`;
    }
}

// 重置所有筛选条件
function resetAllFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('stageFilter').value = '';
    document.getElementById('failureTypeFilter').value = '';
    document.getElementById('lotTypeFilter').value = '';
    
    // 重新筛选
    filterLots();
    
    console.log('🔄 所有筛选条件已重置');
}

// 排序批次数据
function sortLots() {
    filteredLots.sort((a, b) => {
        let aValue = a[sortColumn] || '';
        let bValue = b[sortColumn] || '';
        
        // 处理时间字段
        if (sortColumn === 'timestamp') {
            aValue = new Date(aValue);
            bValue = new Date(bValue);
        }
        // 处理数字字段
        else if (sortColumn === 'GOOD_QTY') {
            aValue = parseInt(aValue) || 0;
            bValue = parseInt(bValue) || 0;
        }
        // 字符串字段
        else {
            aValue = String(aValue).toLowerCase();
            bValue = String(bValue).toLowerCase();
        }
        
        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
        return 0;
    });
}

// 渲染失败批次表格
function renderFailedLots() {
    const tbody = document.getElementById('failedLotsTableBody');
    
    if (filteredLots.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="10" class="text-center py-4">
                    <div class="text-muted">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p class="mb-0">没有找到失败批次数据</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }
    
    // 计算分页
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;
    const paginatedLots = filteredLots.slice(start, end);
    
    tbody.innerHTML = paginatedLots.map(lot => {
        return `
            <tr>
                <td class="lot-id-column">${escapeHtml(lot.LOT_ID || '')}</td>
                <td><span class="badge bg-primary">${escapeHtml(lot.STAGE || '')}</span></td>
                <td class="fw-bold">${escapeHtml(lot.DEVICE || '')}</td>
                <td>${lot.LOT_TYPE ? `<span class="lot-type-badge">${escapeHtml(lot.LOT_TYPE)}</span>` : ''}</td>
                <td>${lot.PKG_PN ? `<span class="pkg-pn-badge">${escapeHtml(lot.PKG_PN)}</span>` : ''}</td>
                <td class="quantity-column">${formatNumber(lot.GOOD_QTY || 0)}</td>
                <td>
                    <span class="${getFailureReasonClass(lot.failure_reason)}">
                        ${escapeHtml(lot.failure_reason || '')}
                    </span>
                </td>
                <td class="suggestion-column">${escapeHtml(lot.suggestion || '')}</td>
                <td class="datetime-value">${formatDateTime(lot.timestamp)}</td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-info btn-sm" 
                                onclick="viewDetails('${escapeHtml(lot.LOT_ID || '')}')" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" 
                                onclick="retryLot('${escapeHtml(lot.LOT_ID || '')}')" title="重试排产">
                            <i class="fas fa-redo"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// 获取失败原因样式类
function getFailureReasonClass(reason) {
    if (!reason) return 'failure-reason-low';
    
    if (reason.includes('配置') || reason.includes('规范')) {
        return 'failure-reason-high';
    } else if (reason.includes('设备') || reason.includes('不兼容')) {
        return 'failure-reason-medium';
    } else {
        return 'failure-reason-low';
    }
}

// 格式化数字
function formatNumber(num) {
    return new Intl.NumberFormat('zh-CN').format(num);
}

// 格式化日期时间
function formatDateTime(dateStr) {
    if (!dateStr) return '';
    try {
        const date = new Date(dateStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (e) {
        return dateStr;
    }
}

// HTML转义
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 更新分页导航
function updatePagination() {
    const totalPages = Math.ceil(filteredLots.length / pageSize);
    const pagination = document.getElementById('pagination');
    
    // 更新记录数显示
    const start = Math.min((currentPage - 1) * pageSize + 1, filteredLots.length);
    const end = Math.min(currentPage * pageSize, filteredLots.length);
    
    document.getElementById('startRecord').textContent = filteredLots.length > 0 ? start : 0;
    document.getElementById('endRecord').textContent = end;
    document.getElementById('totalCount').textContent = filteredLots.length;
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHtml = '';
    
    // 上一页
    paginationHtml += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">‹</a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    paginationHtml += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">›</a>
        </li>
    `;
    
    pagination.innerHTML = paginationHtml;
}

// 切换页面
function changePage(page) {
    const totalPages = Math.ceil(filteredLots.length / pageSize);
    if (page < 1 || page > totalPages) return;
    
    currentPage = page;
    renderFailedLots();
    updatePagination();
}

// 改变页面大小
function changePageSize() {
    pageSize = parseInt(document.getElementById('pageSize').value);
    currentPage = 1;
    renderFailedLots();
    updatePagination();
}

// 清除搜索
function clearSearch() {
    document.getElementById('searchInput').value = '';
    filterLots();
}

// 初始化排序功能
function initializeSorting() {
    document.querySelectorAll('.sortable').forEach(header => {
        header.addEventListener('click', function() {
            const column = this.dataset.column;
            
            if (sortColumn === column) {
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                sortColumn = column;
                sortDirection = 'asc';
            }
            
            // 更新排序图标
            updateSortIcons(column, sortDirection);
            
            // 重新排序和渲染
            sortLots();
            renderFailedLots();
        });
    });
}

// 更新排序图标
function updateSortIcons(activeColumn, direction) {
    document.querySelectorAll('.sortable').forEach(header => {
        const icon = header.querySelector('.sort-icon');
        const column = header.dataset.column;
        
        header.classList.remove('sorted-asc', 'sorted-desc');
        
        if (column === activeColumn) {
            header.classList.add(`sorted-${direction}`);
        }
    });
}

// 功能按钮方法
function retryScheduling() {
    isCurrentOnly = true;
    loadFailedLots();
}

function viewHistory() {
    isCurrentOnly = false;
    loadFailedLots();
}

function refreshData() {
    loadFailedLots();
}

function exportCSV() {
    if (filteredLots.length === 0) {
        alert('没有数据可导出');
        return;
    }
    
    // 构建CSV数据
    const headers = ['内部工单号', '工序', '产品名称', '工单分类', '封装形式', '良品数量', '失败原因', '建议解决方案', '创建时间'];
    const csvData = [
        headers.join(','),
        ...filteredLots.map(lot => [
            lot.LOT_ID || '',
            lot.STAGE || '',
            lot.DEVICE || '',
            lot.LOT_TYPE || '',
            lot.PKG_PN || '',
            lot.GOOD_QTY || 0,
            lot.failure_reason || '',
            lot.suggestion || '',
            formatDateTime(lot.timestamp)
        ].map(field => `"${String(field).replace(/"/g, '""')}"`).join(','))
    ].join('\n');
    
    // 下载CSV文件
    const blob = new Blob(['\uFEFF' + csvData], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `失败批次清单_${new Date().toISOString().slice(0, 19).replace(/[:\-T]/g, '')}.csv`;
    link.click();
}

function clearAllFailed() {
    if (!confirm('确定要清空所有失败批次记录吗？此操作不可恢复！')) {
        return;
    }
    
    fetch('/api/v2/production/clear-failed-lots', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('清空成功');
            loadFailedLots();
        } else {
            alert('清空失败: ' + result.message);
        }
    })
    .catch(error => {
        console.error('清空失败:', error);
        alert('清空失败: ' + error.message);
    });
}

function viewDetails(lotId) {
    const lot = allFailedLots.find(l => l.LOT_ID === lotId);
    if (!lot) {
        alert('找不到批次详情');
        return;
    }
    
    const details = `
内部工单号: ${lot.LOT_ID || ''}
产品名称: ${lot.DEVICE || ''}
工序: ${lot.STAGE || ''}
工单分类: ${lot.LOT_TYPE || ''}
封装形式: ${lot.PKG_PN || ''}
良品数量: ${lot.GOOD_QTY || 0}
失败原因: ${lot.failure_reason || ''}
建议解决方案: ${lot.suggestion || ''}
创建时间: ${formatDateTime(lot.timestamp)}
会话ID: ${lot.session_id || ''}
    `;
    
    alert(details);
}

function retryLot(lotId) {
    if (!confirm(`确定要重试排产批次 ${lotId} 吗？`)) {
        return;
    }
    
    // 这里可以调用重试排产的API
    alert('重试排产功能待实现');
}

// 显示加载状态
function showLoading() {
    document.getElementById('loadingOverlay').style.display = 'flex';
}

// 隐藏加载状态
function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

// 显示错误信息
function showError(message) {
    const tbody = document.getElementById('failedLotsTableBody');
    tbody.innerHTML = `
        <tr>
            <td colspan="10" class="text-center py-4">
                <div class="text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <p class="mb-0">${escapeHtml(message)}</p>
                </div>
            </td>
        </tr>
    `;
}
</script>
{% endblock %} 