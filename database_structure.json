{"et_ft_test_spec": [{"field": "id", "type": "int", "null": "NO", "key": "PRI", "default": null, "extra": "auto_increment"}, {"field": "TEST_SPEC_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TEST_SPEC_NAME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TEST_SPEC_VER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "STAGE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TESTER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "INV_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TEST_SPEC_TYPE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "APPROVAL_STATE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "ACTV_YN", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PROD_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "DEVICE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CHIP_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PKG_PN", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "COMPANY_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "DISABLE_USER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "DISABLE_REASON", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "DISABLE_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "NOTE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "APPROVE_USER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "APPROVE_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "ORT_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "REMAIN_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "STANDARD_YIELD", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "LOW_YIELD", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "DOWN_YIELD", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TEST_AREA", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "HANDLER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TEMPERATURE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FT_PROGRAM", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "QA_PROGRAM", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "GU_PROGRAM", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TB_PN", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "HB_PN", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TIB", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TEST_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "UPH", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "SUFFIX_CODE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TESTER_CONFIG", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "GU_COMPARE_PARAM", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "STA_COMPARE_PARAM", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "DNR", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "SITE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "DPAT", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "BS_NAME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "GU_NAME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "C_SPEC", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TEST_ENG", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TEST_OPERATION", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "ORDER_COMMENT", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "HIGH_YIELD", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "VISION_LOSS_YIELD", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "VISION_YIELD", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "LOSS_YIELD", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "RETEST_YN", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FT_PROGRAM_PATH", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "QA_PROGRAM_PATH", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "GU_PROGRAM_PATH", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EFFECTIVE_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TPL_RULE_TEMP", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TPL_RULE_TEMP_PATH", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "ALARM_DATE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FAC_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EDIT_STATE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EDIT_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EDIT_USER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_KEY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_USER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_MSG", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CREATE_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CREATE_USER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "created_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "DEFAULT_GENERATED"}, {"field": "updated_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "et_wait_lot": [{"field": "id", "type": "int", "null": "NO", "key": "PRI", "default": null, "extra": "auto_increment"}, {"field": "LOT_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "LOT_TYPE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "GOOD_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PROD_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "DEVICE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CHIP_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PKG_PN", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PO_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "STAGE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "WIP_STATE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PROC_STATE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "HOLD_STATE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FLOW_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FLOW_VER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "RELEASE_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FAC_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CREATE_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "created_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "DEFAULT_GENERATED"}, {"field": "updated_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "et_uph_eqp": [{"field": "id", "type": "int", "null": "NO", "key": "PRI", "default": null, "extra": "auto_increment"}, {"field": "DEVICE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PKG_PN", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "STAGE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "UPH", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "SORTER_MODEL", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FAC_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EDIT_STATE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EDIT_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EDIT_USER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_KEY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_USER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_MSG", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CREATE_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CREATE_USER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "created_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "DEFAULT_GENERATED"}, {"field": "updated_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "eqp_status": [{"field": "id", "type": "int", "null": "NO", "key": "PRI", "default": null, "extra": "auto_increment"}, {"field": "HANDLER_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "HANDLER_TYPE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TESTER_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "HANDLER_CONFIG", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "SOCKET_PN", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "KIT_PN", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EQP_CLASS", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EQP_TYPE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TEMPERATURE_RANGE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TEMPERATURE_CAPACITY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "LOT_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "DEVICE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "STATUS", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "HB_PN", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TB_PN", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TESTER_CONFIG", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "STAGE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "created_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "DEFAULT_GENERATED"}, {"field": "updated_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "tcc_inv": [{"field": "id", "type": "int", "null": "NO", "key": "PRI", "default": null, "extra": "auto_increment"}, {"field": "unnamed_0", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "硬件编码", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "关键硬件", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "图片", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "寿命状态", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "仓库", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "初始库位", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "当前储位1", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "当前储位2", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "责任人", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "周期消耗数", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "当前库位", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "封装形式", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "状态", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "类别", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "设备机型", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "寄放方", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "备注_状态_shipout信息_", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "类型", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "状态_1", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "操作", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "created_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "DEFAULT_GENERATED"}, {"field": "updated_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "ct": [{"field": "id", "type": "int", "null": "NO", "key": "PRI", "default": null, "extra": "auto_increment"}, {"field": "LOT_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "WORK_ORDER_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PROD_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "DEVICE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PKG_PN", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CHIP_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FLOW_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "STAGE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "LOT_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "ACT_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "GOOD_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "REJECT_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "LOSS_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "MAIN_EQP_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "AUXILIARY_EQP_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "LOT_START_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "LOT_END_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "SETUP_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FT_TEST_PROGRAM", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "IS_HALF_LOT_DOWN", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FIRST_PASS_YIELD", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FINAL_YIELD", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "VM_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "ALARM_BIN", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_KEY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_USER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_MSG", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CREATE_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CREATE_USER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FAC_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TRACK_CNT", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "COST_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "created_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "DEFAULT_GENERATED"}, {"field": "updated_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "wip_lot": [{"field": "id", "type": "int", "null": "NO", "key": "PRI", "default": null, "extra": "auto_increment"}, {"field": "LOT_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "LOT_TYPE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "DET_LOT_TYPE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "LOT_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "SUB_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "UNIT", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "SUB_UNIT", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "WIP_STATE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PROC_STATE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "HOLD_STATE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "RW_STATE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "REPAIR_STATE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "QC_STATE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PROD_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PROC_RULE_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PRP_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FLOW_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "STAGE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PRP_VER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FLOW_VER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "OPER_VER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CARR_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "USE_SUB_LOT", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "AREA_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "LOC_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EQP_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "SUB_EQP_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PORT_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "RECIPE_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "SUB_RECIPE_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "MARK_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "LOT_IN_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "LOT_OUT_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "GOOD_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "NG_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PREV_PROD_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PREV_PROC_RULE_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PREV_PRP_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PREV_PRP_VER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PREV_FLOW_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PREV_FLOW_VER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PREV_OPER_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PREV_OPER_VER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PREV_EQP_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PREV_PORT_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PREV_RECIPE_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PREV_SUB_RECIPE_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "RTCL_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "BATCH_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "LAST_BATCH_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CTM_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "LOT_GRP_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "RESV_EQP_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "HOT_TYPE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "SEND_COMPANY_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "OPER_CHANGE_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "JOB_START_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "JOB_END_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PLAN_START_DATE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PLAN_DUE_DATE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "GRADE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "REASON_GRP", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "REASON_CODE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FR_RW_PROC_RULE_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FR_RW_PRP_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FR_RW_PRP_VER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FR_RW_FLOW_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FR_RW_FLOW_VER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FR_RW_OPER_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FR_RW_OPER_VER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "RW_RT_PROC_RULE_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "RW_RT_PRP_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "RW_RT_PRP_VER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "RW_RT_FLOW_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "RW_RT_FLOW_VER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "RW_RT_OPER_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "RW_RT_OPER_VER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PILOT_TYPE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "MERGE_OPER_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "ACT_NM", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "LOT_JUDGE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FAC_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "SUB_FAC", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "ROOT_LOT_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PARENT_LOT_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CHILD_LOT_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "LOT_OBJ_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CUST_LOT_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "WORK_ORDER_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "WORK_ORDER_VER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "BOM_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "BOM_VER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PO_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "LOT_OWNER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PKG_PN", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "RELEASE_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "SHIP_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "SHIP_ORDER_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "SHIP_FAC_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CREATE_LOT_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CREATE_SUB_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "ROOT_LOT_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TRACK_CARD_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "DBP_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CJOB_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PROC_CNT", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "RETEST_YN", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "DUT_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_KEY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_USER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_MSG", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CREATE_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CREATE_USER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "STR_FLAG", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "MAIN_EQP_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "AUXILIARY_EQP_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CONTAINER_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CHIP_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "ACT_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TEST_SPEC_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TEST_SPEC_NAME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "TEST_SPEC_VER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "ORT_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "OA_FLAG", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "DEVICE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "WAREHOUSE_CONTAINER_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PROD_THICKNESS", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "IQC_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "UPH", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "SEAL_FLAG", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PACK_SPEC_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PACK_SPEC_VER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FULL_INSP_QC", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "SPLIT_TYPE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "WH_LOCATION_NO", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "RELEASE_HOLD_TYPE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "DATA_CONFIRM_HOLD_YN", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "ORT_SAMP_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "IQC_SAMP_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "LOCATION", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "RETEST_FLOW_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "HALF_LOT_HOLD", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "MERGE_LOT_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "STRM_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "STRM_SAMP_QTY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "created_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "DEFAULT_GENERATED"}, {"field": "updated_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "et_recipe_file": [{"field": "id", "type": "int", "null": "NO", "key": "PRI", "default": null, "extra": "auto_increment"}, {"field": "PROD_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "COMPANY_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "STAGE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "DEVICE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CHIP_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PKG_PN", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "RECIPE_FILE_NAME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "RECIPE_FILE_PATH", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "APPROVAL_STATE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FAC_ID", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EDIT_STATE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EDIT_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EDIT_USER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_KEY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_USER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EVENT_MSG", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CREATE_TIME", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "CREATE_USER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "PROD_TYPE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "EQP_TYPE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "HANDLER_CONFIG", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "SIMP_RECIPE_FILE_PATH", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "RECIPE_VER", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "SUB_FAC", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "KIT_PN", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "SOCKET_PN", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "FAMILY", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "COORDINATE_ONE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "COORDINATE_TWO", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "COORDINATE_THREE", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "created_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "DEFAULT_GENERATED"}, {"field": "updated_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}]}