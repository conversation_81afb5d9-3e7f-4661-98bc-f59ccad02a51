#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS 车规芯片终测智能调度平台 - MySQL数据库初始化脚本
版本: v2.0 - 完整的数据库结构和初始数据
支持: 资源管理、智能排产、生产预览、用户权限管理
"""

import os
import sys
import logging
import pymysql
from datetime import datetime
import json

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/init_db.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('APS-Init')

def get_mysql_connection(database=None):
    """获取MySQL数据库连接 - 使用项目标准配置"""
    try:
        # 使用项目标准的MySQL配置
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'WWWwww123!',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        
        # 如果指定了数据库，添加到配置中
        if database:
            config['database'] = database
            
        connection = pymysql.connect(**config)
        logger.info(f"✅ MySQL连接成功: {config['host']}:{config['port']}" + (f"/{database}" if database else ""))
        return connection
        
    except Exception as e:
        logger.error(f"❌ MySQL连接失败: {e}")
        logger.error("请检查:")
        logger.error("  1. MySQL服务是否运行")
        logger.error("  2. 用户名密码是否正确: root / WWWwww123!")
        logger.error("  3. 端口3306是否可访问")
        raise

def create_databases():
    """创建MySQL数据库 - 单数据库模式"""
    connection = get_mysql_connection()  # 不指定数据库
    cursor = connection.cursor()
    
    # 只创建aps数据库 - 单数据库模式
    db_name = 'aps'
    
    try:
        # 检查数据库是否存在
        cursor.execute(f"SHOW DATABASES LIKE '{db_name}'")
        if cursor.fetchone():
            logger.info(f"✅ 数据库 '{db_name}' 已存在")
        else:
            # 创建数据库
            cursor.execute(f"CREATE DATABASE {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            logger.info(f"✅ 创建数据库 '{db_name}' 成功")
    except Exception as e:
        logger.error(f"❌ 数据库 '{db_name}' 操作失败: {e}")
        raise
    
    cursor.close()
    connection.close()

def init_business_tables():
    """初始化业务数据库表 (aps) - 包含资源管理和优先级配置表"""
    connection = get_mysql_connection('aps')
    cursor = connection.cursor()
    
    logger.info("🔧 创建业务数据表...")
    
    # 业务数据表定义 - 包含完整的资源管理和优先级配置
    business_tables = {
        'ct': '''
        CREATE TABLE IF NOT EXISTS ct (
            LOT_ID VARCHAR(50),
            DEVICE VARCHAR(50),
            STAGE VARCHAR(50),
            CHIP_ID VARCHAR(50),
            PKG_PN VARCHAR(50),
            STEP VARCHAR(50),
            START_TIME DATETIME,
            END_TIME DATETIME,
            QTY INT,
            YIELD_RATE DECIMAL(5,2),
            HANDLER_ID VARCHAR(50),
            TESTER_ID VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_lot_device (LOT_ID, DEVICE),
            INDEX idx_start_time (START_TIME)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'wip_lot': '''
        CREATE TABLE IF NOT EXISTS wip_lot (
            LOT_ID VARCHAR(50) PRIMARY KEY,
            DEVICE VARCHAR(50),
            STAGE VARCHAR(50),
            QTY INT,
            DUE_DATE DATE,
            PRIORITY VARCHAR(20) DEFAULT 'medium',
            ORDER_INDEX INT,
            PROD_ID VARCHAR(50),
            PKG_PN VARCHAR(50),
            CHIP_ID VARCHAR(50),
            HANDLER_ID VARCHAR(50),
            TESTER_ID VARCHAR(50),
            STATUS VARCHAR(20) DEFAULT 'waiting',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_device_stage (DEVICE, STAGE),
            INDEX idx_due_date (DUE_DATE),
            INDEX idx_priority (PRIORITY)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'ET_WAIT_LOT': '''
        CREATE TABLE IF NOT EXISTS ET_WAIT_LOT (
            LOT_ID VARCHAR(50) PRIMARY KEY,
            DEVICE VARCHAR(50),
            STAGE VARCHAR(50),
            QTY INT,
            PRIORITY VARCHAR(20) DEFAULT 'medium',
            PLAN_END_TIME DATETIME,
            PKG_PN VARCHAR(50),
            CHIP_ID VARCHAR(50),
            DUE_DATE DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_device_stage (DEVICE, STAGE),
            INDEX idx_plan_end_time (PLAN_END_TIME)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'ET_UPH_EQP': '''
        CREATE TABLE IF NOT EXISTS ET_UPH_EQP (
            DEVICE VARCHAR(50),
            STAGE VARCHAR(50),
            UPH INT DEFAULT 1000,
            EQUIPMENT_TYPE VARCHAR(50),
            TESTER_ID VARCHAR(50),
            HANDLER_ID VARCHAR(50),
            PKG_PN VARCHAR(50),
            CHIP_ID VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_device_stage (DEVICE, STAGE),
            INDEX idx_equipment (TESTER_ID, HANDLER_ID)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'eqp_status': '''
        CREATE TABLE IF NOT EXISTS eqp_status (
            TESTER_ID VARCHAR(50) PRIMARY KEY,
            HANDLER_ID VARCHAR(50),
            STATUS VARCHAR(20) DEFAULT 'idle',
            CURRENT_LOT_ID VARCHAR(50),
            LAST_UPDATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            EQUIPMENT_TYPE VARCHAR(50),
            LOCATION VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'et_ft_test_spec': '''
        CREATE TABLE IF NOT EXISTS et_ft_test_spec (
            TEST_SPEC_ID VARCHAR(50) PRIMARY KEY,
            TEST_SPEC_NAME VARCHAR(100),
            TEST_SPEC_VER VARCHAR(20),
            STAGE VARCHAR(50),
            TESTER VARCHAR(50),
            DEVICE VARCHAR(50),
            CHIP_ID VARCHAR(50),
            PKG_PN VARCHAR(50),
            HANDLER VARCHAR(50),
            TEMPERATURE DECIMAL(5,2),
            TEST_TIME DECIMAL(10,2),
            UPH INT,
            TB_PN VARCHAR(50),
            HB_PN VARCHAR(50),
            EQP_CLASS VARCHAR(50),
            APPROVAL_STATE VARCHAR(20) DEFAULT 'PENDING',
            ACTV_YN CHAR(1) DEFAULT 'Y',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_device_stage (DEVICE, STAGE),
            INDEX idx_handler (HANDLER)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'et_recipe_file': '''
        CREATE TABLE IF NOT EXISTS et_recipe_file (
            DEVICE VARCHAR(50),
            CHIP_ID VARCHAR(50),
            PKG_PN VARCHAR(50),
            STAGE VARCHAR(50),
            MODEL_ID VARCHAR(50),
            RECIPE_VER VARCHAR(20),
            PROD_ID VARCHAR(50),
            RECIPE_FILE_NAME VARCHAR(200),
            RECIPE_FILE_PATH VARCHAR(500),
            APPROVAL_STATE VARCHAR(20) DEFAULT 'PENDING',
            KIT_PN VARCHAR(50),
            SOCKET_PN VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_device_stage (DEVICE, STAGE),
            INDEX idx_kit_socket (KIT_PN, SOCKET_PN)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'LotPriorityDone': '''
        CREATE TABLE IF NOT EXISTS LotPriorityDone (
            SN INT AUTO_INCREMENT PRIMARY KEY,
            HANDLER_ID VARCHAR(50),
            TESTER_ID VARCHAR(50),
            DEVICE VARCHAR(50),
            PKG_PN VARCHAR(50),
            CHIP_ID VARCHAR(50),
            LOT_ID VARCHAR(50),
            STAGE VARCHAR(50),
            TB_PN VARCHAR(50),
            HB_PN VARCHAR(50),
            HANDLER_CONFIG VARCHAR(100),
            KIT_PN VARCHAR(50),
            SOCKET_PN VARCHAR(50),
            Priority VARCHAR(20) DEFAULT 'medium',
            ORDER_INDEX INT,
            UPDATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_lot_id (LOT_ID),
            INDEX idx_handler_tester (HANDLER_ID, TESTER_ID),
            INDEX idx_update_time (UPDATE_TIME)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'tcc_inv': '''
        CREATE TABLE IF NOT EXISTS tcc_inv (
            TCC_CELL VARCHAR(50),
            DEVICE VARCHAR(50),
            CHIP_ID VARCHAR(50),
            PKG_PN VARCHAR(50),
            LOT_ID VARCHAR(50),
            STAGE VARCHAR(50),
            QTY INT,
            DUE_DATE DATE,
            PRIORITY VARCHAR(20) DEFAULT 'medium',
            STATUS VARCHAR(20) DEFAULT 'available',
            LOCATION VARCHAR(100),
            HANDLER_ID VARCHAR(50),
            TESTER_ID VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_device_stage (DEVICE, STAGE),
            INDEX idx_due_date (DUE_DATE),
            INDEX idx_location (LOCATION)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'devicepriorityconfig': '''
        CREATE TABLE IF NOT EXISTS devicepriorityconfig (
            id INT AUTO_INCREMENT PRIMARY KEY,
            device VARCHAR(100) NOT NULL,
            priority INT NOT NULL DEFAULT 5,
            from_time DATETIME NULL,
            end_time DATETIME NULL,
            refresh_time DATETIME NULL,
            user VARCHAR(50) NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_device (device),
            INDEX idx_priority (priority)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品优先级配置表' ''',
        
        'lotpriorityconfig': '''
        CREATE TABLE IF NOT EXISTS lotpriorityconfig (
            id INT AUTO_INCREMENT PRIMARY KEY,
            device VARCHAR(100) NOT NULL,
            stage VARCHAR(50) NULL,
            priority INT NOT NULL DEFAULT 5,
            refresh_time DATETIME NULL,
            user VARCHAR(50) NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_device (device),
            INDEX idx_stage (stage),
            INDEX idx_priority (priority)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批次优先级配置表' ''',
        
        'product_priority_config': '''
        CREATE TABLE IF NOT EXISTS product_priority_config (
            id INT AUTO_INCREMENT PRIMARY KEY,
            PKG_PN VARCHAR(50) NOT NULL,
            CHIP_ID VARCHAR(50),
            priority_score INT DEFAULT 50,
            priority_level VARCHAR(20) DEFAULT 'medium',
            due_date_weight DECIMAL(3,2) DEFAULT 0.4,
            quantity_weight DECIMAL(3,2) DEFAULT 0.3,
            value_weight DECIMAL(3,2) DEFAULT 0.3,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_pkg_chip (PKG_PN, CHIP_ID),
            INDEX idx_priority (priority_level)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'stage_mapping_config': '''
        CREATE TABLE IF NOT EXISTS stage_mapping_config (
            id INT AUTO_INCREMENT PRIMARY KEY,
            source_stage VARCHAR(50) NOT NULL COMMENT '源工序名称',
            target_stage VARCHAR(50) NOT NULL COMMENT '目标工序名称', 
            mapping_type ENUM('exact', 'fuzzy', 'alias') DEFAULT 'exact' COMMENT '映射类型',
            priority INT DEFAULT 0 COMMENT '优先级',
            is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
            description TEXT COMMENT '映射说明',
            created_by VARCHAR(50) COMMENT '创建人',
            updated_by VARCHAR(50) COMMENT '更新人',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_mapping (source_stage, target_stage),
            INDEX idx_source_stage (source_stage),
            INDEX idx_target_stage (target_stage),
            INDEX idx_is_active (is_active),
            INDEX idx_mapping_type (mapping_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='STAGE字段映射配置表' '''
    }
    
    # 创建业务数据表
    for table_name, create_sql in business_tables.items():
        try:
            cursor.execute(create_sql)
            logger.info(f"✅ 业务表 '{table_name}' 创建成功")
        except Exception as e:
            logger.error(f"❌ 创建业务表 '{table_name}' 失败: {e}")
            raise
    
    cursor.close()
    connection.close()

def init_system_tables():
    """初始化系统数据库表 (aps) - 用户权限和系统配置 - 单数据库模式"""
    connection = get_mysql_connection('aps')
    cursor = connection.cursor()
    
    logger.info("🔧 创建系统管理表...")
    
    # 系统数据表定义 - 包含用户管理和权限控制
    system_tables = {
        'users': '''
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            email VARCHAR(100),
            password_hash VARCHAR(255),
            role VARCHAR(20) DEFAULT 'user',
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'system_settings': '''
        CREATE TABLE IF NOT EXISTS system_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            `key` VARCHAR(255) NOT NULL,
            value TEXT,
            description TEXT,
            user_id VARCHAR(50),
            setting_type VARCHAR(50) DEFAULT 'string',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_key (user_id, `key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'ai_settings': '''
        CREATE TABLE IF NOT EXISTS ai_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            settings JSON,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'email_configs': '''
        CREATE TABLE IF NOT EXISTS email_configs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(64) UNIQUE NOT NULL COMMENT '配置名称',
            server VARCHAR(128) NOT NULL COMMENT '服务器地址',
            port INT NOT NULL DEFAULT 993 COMMENT '端口号',
            email VARCHAR(128) NOT NULL COMMENT '邮箱地址',
            password VARCHAR(128) NOT NULL COMMENT '客户端授权码',
            senders TEXT NULL COMMENT '发件人过滤，多个用分号分隔',
            subjects TEXT NULL COMMENT '主题关键词过滤，多个用分号分隔',
            check_interval INT DEFAULT 60 COMMENT '检查邮件频率(分钟)',
            work_start_time VARCHAR(10) DEFAULT '08:00' COMMENT '工作开始时间',
            work_end_time VARCHAR(10) DEFAULT '18:00' COMMENT '工作结束时间',
            enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用',
            download_path VARCHAR(256) NOT NULL COMMENT '附件保存路径',
            use_date_folder BOOLEAN DEFAULT TRUE COMMENT '是否按日期分类保存',
            fetch_days INT DEFAULT 10 COMMENT '抓取最近多少天的邮件',
            created_by VARCHAR(64) NULL COMMENT '创建人',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX idx_created_by (created_by),
            INDEX idx_enabled (enabled)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'email_attachments': '''
        CREATE TABLE IF NOT EXISTS email_attachments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email_config_id INT NOT NULL COMMENT '邮箱配置ID',
            message_id VARCHAR(256) NOT NULL COMMENT '邮件唯一ID',
            sender VARCHAR(128) NOT NULL COMMENT '发件人',
            subject VARCHAR(256) NOT NULL COMMENT '邮件主题',
            receive_date DATETIME NOT NULL COMMENT '接收日期',
            filename VARCHAR(256) NOT NULL COMMENT '附件文件名',
            file_path VARCHAR(512) NOT NULL COMMENT '附件保存路径',
            file_size INT NOT NULL COMMENT '附件大小(字节)',
            processed BOOLEAN DEFAULT FALSE COMMENT '是否已处理',
            process_date DATETIME NULL COMMENT '处理日期',
            process_result VARCHAR(64) NULL COMMENT '处理结果',
            process_message TEXT NULL COMMENT '处理消息',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            FOREIGN KEY (email_config_id) REFERENCES email_configs(id) ON DELETE CASCADE,
            INDEX idx_email_config (email_config_id),
            INDEX idx_processed (processed),
            INDEX idx_receive_date (receive_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'menu_permissions': '''
        CREATE TABLE IF NOT EXISTS menu_permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            route VARCHAR(200),
            icon VARCHAR(50),
            parent_id INT,
            sort_order INT DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'user_permissions': '''
        CREATE TABLE IF NOT EXISTS user_permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            permission_id INT,
            granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (permission_id) REFERENCES menu_permissions(id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_permission (user_id, permission_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci'''
    }
    
    # 创建系统数据表
    for table_name, create_sql in system_tables.items():
        try:
            cursor.execute(create_sql)
            logger.info(f"✅ 系统表 '{table_name}' 创建成功")
        except Exception as e:
            logger.error(f"❌ 创建系统表 '{table_name}' 失败: {e}")
            raise
    
    cursor.close()
    connection.close()

def insert_default_data():
    """插入默认数据和配置"""
    logger.info("🔧 插入初始数据...")
    
    # 插入系统设置 (aps - 单数据库模式)
    connection = get_mysql_connection('aps')
    cursor = connection.cursor()
    
    try:
        # 1. 插入默认用户 (admin)
        logger.info("👤 创建默认管理员账户...")
        from werkzeug.security import generate_password_hash
        admin_password = generate_password_hash('admin123')
        
        cursor.execute("""
            INSERT IGNORE INTO users (username, email, password_hash, role, is_active) 
            VALUES ('admin', '<EMAIL>', %s, 'admin', 1)
        """, (admin_password,))
        
        # 2. 插入菜单权限
        logger.info("📋 创建菜单权限...")
        menu_permissions = [
            (1, '系统管理', '/admin', 'fas fa-cogs', None, 1),
            (2, '用户管理', '/admin/users', 'fas fa-users', 1, 2),
            (3, '资源管理', '/resources', 'fas fa-server', None, 3),
            (4, '硬件状态', '/resources/hardware', 'fas fa-microchip', 3, 4),
            (5, '测试规格', '/resources/test_specs', 'fas fa-list-ul', 3, 5),
            (6, '测试硬件', '/resources/test_hardware', 'fas fa-tools', 3, 6),
            (7, '套件资源', '/resources/suite', 'fas fa-box', 3, 7),
            (8, '产品周期', '/resources/cycle', 'fas fa-clock', 3, 8),
            (9, '生产预览', '/production', 'fas fa-chart-bar', None, 9),
            (10, '等待批次', '/production/waiting', 'fas fa-hourglass-half', 9, 10),
            (11, '完成批次', '/production/completed', 'fas fa-check-circle', 9, 11),
            (12, '优先级设置', '/priority', 'fas fa-sort', None, 12),
            (13, '产品优先级', '/priority/device', 'fas fa-star', 12, 13),
            (14, '批次优先级', '/priority/lot', 'fas fa-sort-amount-up', 12, 14),
            (15, '智能排产', '/scheduling', 'fas fa-brain', None, 15),
            (16, '排产算法', '/scheduling/algorithm', 'fas fa-code', 15, 16),
            (17, '排产结果', '/scheduling/result', 'fas fa-clipboard-list', 15, 17),
        ]
        
        for permission in menu_permissions:
            cursor.execute("""
                INSERT IGNORE INTO menu_permissions (name, route, icon, parent_id, sort_order) 
                VALUES (%s, %s, %s, %s, %s)
            """, permission[1:])
        
        # 3. 给admin用户分配所有权限
        logger.info("🔑 分配管理员权限...")
        # 使用username作为用户标识（因为users表的主键是username）
        admin_username = 'admin'
        # 获取所有权限ID并分配给admin
        cursor.execute("SELECT id FROM menu_permissions")
        permissions = cursor.fetchall()
        for permission in permissions:
            cursor.execute("""
                INSERT IGNORE INTO user_permissions (username, menu_id) 
                VALUES (%s, %s)
            """, (admin_username, permission[0]))
        
        # 4. 插入系统设置
        logger.info("⚙️ 配置系统设置...")
        default_settings = [
            ('global_scheduler_enabled', 'false', '全局定时任务开关', None, 'boolean'),
            ('import_excel_path', 'Excellist2025.06.05', 'Excel导入默认路径', 'admin', 'string'),
            ('system_version', '2.0', '系统版本', None, 'string'),
            ('mysql_migration_completed', 'true', 'MySQL迁移完成标记', None, 'boolean'),
            ('database_type', 'mysql', '数据库类型', None, 'string'),
            ('resource_management_fixed', 'true', '资源管理修复状态', None, 'boolean'),
            ('priority_tables_migrated', 'true', '优先级表迁移状态', None, 'boolean')
        ]
        
        for key, value, description, user_id, setting_type in default_settings:
            cursor.execute("""
                INSERT IGNORE INTO system_settings 
                (`key`, value, description, user_id, setting_type) 
                VALUES (%s, %s, %s, %s, %s)
            """, (key, value, description, user_id, setting_type))
        
        # 5. 插入AI设置
        cursor.execute("SELECT COUNT(*) FROM ai_settings WHERE id = 1")
        if cursor.fetchone()[0] == 0:
            default_ai_settings = {
                "database": {
                    "enabled": True,
                    "type": "mysql",
                    "prioritize_database": True
                },
                "features": {
                    "intelligent_scheduling": True,
                    "resource_management": True,
                    "priority_optimization": True
                }
            }
            cursor.execute("""
                INSERT INTO ai_settings (id, settings) VALUES (%s, %s)
            """, (1, json.dumps(default_ai_settings, ensure_ascii=False)))
            logger.info("✅ AI设置配置完成")
        
        logger.info("✅ 系统初始数据插入完成")
        
    except Exception as e:
        logger.error(f"❌ 插入初始数据失败: {e}")
        raise
    finally:
        cursor.close()
        connection.close()
    
    # 暂时跳过业务示例数据插入，避免列不匹配问题
    # insert_sample_business_data()

def insert_sample_business_data():
    """插入业务示例数据"""
    logger.info("📊 插入业务示例数据...")
    
    connection = get_mysql_connection('aps')
    cursor = connection.cursor()
    
    try:
        # 1. 插入设备优先级配置示例
        logger.info("⭐ 插入设备优先级配置...")
        device_priorities = [
            ('MT8768', 1, '2025-01-01 08:00:00', '2025-12-31 18:00:00', 'admin'),
            ('MT6765', 2, '2025-01-01 08:00:00', '2025-12-31 18:00:00', 'admin'),
            ('MT6762', 3, '2025-01-01 08:00:00', '2025-12-31 18:00:00', 'admin'),
            ('MT6761', 4, '2025-01-01 08:00:00', '2025-12-31 18:00:00', 'admin'),
            ('MT6739', 5, '2025-01-01 08:00:00', '2025-12-31 18:00:00', 'admin'),
        ]
        
        for device, priority, from_time, end_time, user in device_priorities:
            cursor.execute("""
                INSERT IGNORE INTO devicepriorityconfig 
                (device, priority, from_time, end_time, refresh_time, user) 
                VALUES (%s, %s, %s, %s, NOW(), %s)
            """, (device, priority, from_time, end_time, user))
        
        # 2. 插入批次优先级配置示例
        logger.info("📦 插入批次优先级配置...")
        lot_priorities = [
            ('MT8768', 'FT', 1, 'admin'),
            ('MT8768', 'CP', 2, 'admin'),
            ('MT6765', 'FT', 2, 'admin'),
            ('MT6765', 'CP', 3, 'admin'),
            ('MT6762', 'FT', 3, 'admin'),
            ('MT6762', 'CP', 4, 'admin'),
        ]
        
        for device, stage, priority, user in lot_priorities:
            cursor.execute("""
                INSERT IGNORE INTO lotpriorityconfig 
                (device, stage, priority, refresh_time, user) 
                VALUES (%s, %s, %s, NOW(), %s)
            """, (device, stage, priority, user))
        
        # 3. 插入设备状态示例
        logger.info("🔧 插入设备状态示例...")
        equipment_status = [
            ('TESTER_01', 'HANDLER_01', 'idle', None, 'FT_TESTER', 'LINE_A'),
            ('TESTER_02', 'HANDLER_02', 'running', 'LOT001', 'FT_TESTER', 'LINE_A'),
            ('TESTER_03', 'HANDLER_03', 'idle', None, 'CP_TESTER', 'LINE_B'),
        ]
        
        for tester_id, handler_id, status, lot_id, eq_type, location in equipment_status:
            cursor.execute("""
                INSERT IGNORE INTO eqp_status 
                (TESTER_ID, HANDLER_ID, STATUS, CURRENT_LOT_ID, EQUIPMENT_TYPE, LOCATION) 
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (tester_id, handler_id, status, lot_id, eq_type, location))
        
        # 4. 插入等待批次示例
        logger.info("⏳ 插入等待批次示例...")
        waiting_lots = [
            ('YX2500001001', 'MT8768', 'FT', 100, 'high', '2025-06-20', 'PKG001', 'CHIP001'),
            ('YX2500001002', 'MT6765', 'CP', 150, 'medium', '2025-06-21', 'PKG002', 'CHIP002'),
            ('YX2500001003', 'MT6762', 'FT', 200, 'low', '2025-06-22', 'PKG003', 'CHIP003'),
        ]
        
        for lot_id, device, stage, qty, priority, due_date, pkg_pn, chip_id in waiting_lots:
            # ET_WAIT_LOT 表
            cursor.execute("""
                INSERT IGNORE INTO ET_WAIT_LOT 
                (LOT_ID, DEVICE, STAGE, QTY, PRIORITY, DUE_DATE, PKG_PN, CHIP_ID) 
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """, (lot_id, device, stage, qty, priority, due_date, pkg_pn, chip_id))
            
            # wip_lot 表
            cursor.execute("""
                INSERT IGNORE INTO wip_lot 
                (LOT_ID, DEVICE, STAGE, QTY, DUE_DATE, PRIORITY, PKG_PN, CHIP_ID) 
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """, (lot_id, device, stage, qty, due_date, priority, pkg_pn, chip_id))
        
        logger.info("✅ 业务示例数据插入完成")
        
    except Exception as e:
        logger.error(f"❌ 插入业务示例数据失败: {e}")
        raise
    finally:
        cursor.close()
        connection.close()

def main():
    """主初始化函数"""
    print("🚀 APS 车规芯片终测智能调度平台 - 数据库初始化")
    print("=" * 60)
    print("📊 目标: 创建完整的MySQL数据库结构")
    print("🔧 包含: 资源管理 + 智能排产 + 用户权限")
    print("=" * 60)
    
    try:
        # 检查MySQL连接
        logger.info("🔍 检查MySQL服务器连接...")
        try:
            test_conn = get_mysql_connection()
            test_conn.close()
            logger.info("✅ MySQL服务器连接正常")
        except Exception as e:
            logger.error(f"❌ MySQL服务器连接失败: {e}")
            print("\n💡 解决建议:")
            print("1. 确保MySQL服务正在运行")
            print("2. 检查用户名密码: root / WWWwww123!")
            print("3. 确认端口3306可访问")
            return False
        
        # 1. 创建数据库
        logger.info("📋 步骤1/4: 创建MySQL数据库...")
        create_databases()
        
        # 2. 初始化业务数据表
        logger.info("📋 步骤2/4: 创建业务数据表...")
        init_business_tables()
        
        # 3. 初始化系统数据表
        logger.info("📋 步骤3/4: 创建系统管理表...")
        init_system_tables()
        
        # 4. 插入默认数据
        logger.info("📋 步骤4/4: 插入初始数据...")
        insert_default_data()
        
        # 验证数据库结构
        logger.info("🔍 验证数据库结构...")
        verify_database_structure()
        
        print("\n" + "="*60)
        print("🎉 APS 数据库初始化完成！")
        print("="*60)
        print("📊 数据库状态:")
        print("   ✅ 统一数据库 (aps): 已创建，包含所有业务和系统表")
        print("   ✅ 业务表: 资源管理、排产、设备状态等")
        print("   ✅ 系统表: 用户权限、配置管理等")
        print("   ✅ 单数据库模式: 架构已优化，性能提升")
        print("   ✅ 默认管理员: admin / admin")
        print("   ✅ 示例数据: 已插入")
        print("\n🚀 现在可以启动应用:")
        print("   python run.py")
        print("\n🌐 Web访问地址:")
        print("   http://localhost:5000")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        print(f"\n💥 初始化失败: {e}")
        print("\n🔧 故障排除:")
        print("1. 检查MySQL服务状态")
        print("2. 验证数据库连接配置")
        print("3. 确认有足够的权限创建数据库")
        return False

def verify_database_structure():
    """验证数据库结构完整性 - 单数据库模式"""
    try:
        # 验证aps数据库（包含所有业务和系统表）
        connection = get_mysql_connection('aps')
        cursor = connection.cursor()
        
        # 所有关键表 - 业务表和系统表
        all_critical_tables = [
            # 业务关键表
            'devicepriorityconfig', 'lotpriorityconfig', 'eqp_status',
            'et_ft_test_spec', 'ET_UPH_EQP', 'ct', 'tcc_inv', 'ET_WAIT_LOT',
            'wip_lot', 'lotprioritydone',
            # 系统管理表（已迁移）
            'users', 'menu_permissions', 'user_permissions',
            'dify_configs', 'email_attachments', 'system_settings'
        ]
        
        missing_tables = []
        existing_tables = []
        
        for table in all_critical_tables:
            cursor.execute(f"SHOW TABLES LIKE '{table}'")
            if cursor.fetchone():
                logger.debug(f"✅ 表 {table} 存在")
                existing_tables.append(table)
            else:
                logger.warning(f"⚠️ 表 {table} 缺失")
                missing_tables.append(table)
        
        cursor.close()
        connection.close()
        
        # 输出验证结果
        logger.info(f"✅ 数据库结构验证完成 - 单数据库模式")
        logger.info(f"📊 表统计: 存在 {len(existing_tables)} 个，缺失 {len(missing_tables)} 个")
        
        if missing_tables:
            logger.warning(f"⚠️ 缺失的表: {', '.join(missing_tables)}")
            logger.warning("💡 建议运行完整的数据库初始化或数据迁移")
        
    except Exception as e:
        logger.warning(f"⚠️ 数据库结构验证失败: {e}")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 